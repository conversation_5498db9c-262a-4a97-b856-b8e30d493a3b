const { pool } = require('../config/config');
const ChallengeUserMappingModel = require('../models/challengeUserMappingModel');

// Mock dependencies
jest.mock('../config/config', () => ({
  pool: {
    query: jest.fn(),
    getConnection: jest.fn()
  }
}));

describe('Winner Check Tests', () => {
  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();
  });

  test('checkWinner should correctly identify a winner', async () => {
    // Arrange
    const challengeId = 123;
    const userId = 456;
    
    // Mock challenge data with winning rules
    const challengeData = {
      id: challengeId,
      name: 'Test Challenge',
      winning_rules: JSON.stringify({
        level: 5,
        winning_points: 500,
        rank: 1
      })
    };
    
    // Mock user progress data that meets winning criteria
    const userProgressData = {
      userId: userId,
      challenge_id: challengeId,
      score: 600, // More than winning_points
      level: 6,   // More than required level
      rank: 1     // Equal to required rank
    };
    
    // Mock pool.query responses
    pool.query.mockImplementation((query, params) => {
      if (query.includes('SELECT * FROM tbl_challenges')) {
        return [[challengeData]];
      } else if (query.includes('SELECT * FROM tbl_challenge_user_mapping')) {
        return [[userProgressData]];
      }
      return [[]];
    });
    
    // Act
    const result = await ChallengeUserMappingModel.checkWinner(challengeId, userId);
    
    // Assert
    expect(pool.query).toHaveBeenCalledTimes(2);
    expect(result.isWinner).toBe(true);
    expect(result.winningRules).toEqual({
      level: 5,
      winning_points: 500,
      rank: 1
    });
  });
  
  test('checkWinner should correctly identify a loser', async () => {
    // Arrange
    const challengeId = 123;
    const userId = 456;
    
    // Mock challenge data with winning rules
    const challengeData = {
      id: challengeId,
      name: 'Test Challenge',
      winning_rules: JSON.stringify({
        level: 5,
        winning_points: 500,
        rank: 1
      })
    };
    
    // Mock user progress data that does NOT meet winning criteria
    const userProgressData = {
      userId: userId,
      challenge_id: challengeId,
      score: 400, // Less than winning_points
      level: 4,   // Less than required level
      rank: 2     // Worse than required rank
    };
    
    // Mock pool.query responses
    pool.query.mockImplementation((query, params) => {
      if (query.includes('SELECT * FROM tbl_challenges')) {
        return [[challengeData]];
      } else if (query.includes('SELECT * FROM tbl_challenge_user_mapping')) {
        return [[userProgressData]];
      }
      return [[]];
    });
    
    // Act
    const result = await ChallengeUserMappingModel.checkWinner(challengeId, userId);
    
    // Assert
    expect(pool.query).toHaveBeenCalledTimes(2);
    expect(result.isWinner).toBe(false);
    expect(result.winningRules).toEqual({
      level: 5,
      winning_points: 500,
      rank: 1
    });
  });
  
  test('addWinner should add a user to the winners table', async () => {
    // Arrange
    const challengeId = 123;
    const userId = 456;
    const insertId = 789;
    
    // Mock that the user is not already a winner
    pool.query.mockImplementationOnce(() => [[]]);
    
    // Mock the insert operation
    pool.query.mockImplementationOnce(() => [{
      insertId
    }]);
    
    // Act
    const result = await ChallengeUserMappingModel.addWinner(challengeId, userId);
    
    // Assert
    expect(pool.query).toHaveBeenCalledTimes(2);
    expect(result).toEqual({
      success: true,
      message: 'User added as winner successfully',
      isNewWinner: true,
      winnerId: insertId
    });
  });
  
  test('addWinner should handle when user is already a winner', async () => {
    // Arrange
    const challengeId = 123;
    const userId = 456;
    
    // Mock that the user is already a winner
    pool.query.mockImplementationOnce(() => [[{
      id: 789,
      userId,
      challengeId
    }]]);
    
    // Act
    const result = await ChallengeUserMappingModel.addWinner(challengeId, userId);
    
    // Assert
    expect(pool.query).toHaveBeenCalledTimes(1);
    expect(result).toEqual({
      success: true,
      message: 'User is already a winner',
      isNewWinner: false
    });
  });
});
