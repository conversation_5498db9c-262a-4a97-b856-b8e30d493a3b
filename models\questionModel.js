const { pool } = require('../config/config');
const AskedQuestion = require('./askedQuestionModel');

class Question {
    constructor(question) {
        this.id = question.id;
        this.question = question.question;
        this.time = question.time;
        this.type = question.type;
        this.isCloud = question.isCloud;
        this.question_category_id = question.question_category_id;
        this.file_path = !question.isCloud?this.formatFilePath(question.file_path, question.type):question.file_path;
        this.strength = question.strength;
        this.choices = question.choices || [];
        this.category = question.category || null;
    }

    formatFilePath(filePath, type) {
        if (!filePath) return null;
        if (!['image', 'video', 'audio'].includes(type)) return null;

        // If the file path already contains the base URL, return it as is
        if (filePath.startsWith('http')) {
            return filePath;
        }

        // Base URL for your media files
        const baseUrl = process.env.BASE_URL || 'http://localhost:4000';
        
        // Extract just the filename, removing any path prefixes
        const filename = filePath.split('/').pop();
        
        // Construct the correct path based on media type
        switch (type) {
            case 'image':
                return `${baseUrl}/uploads/images/${filename}`;
            case 'video':
                return `${baseUrl}/uploads/videos/${filename}`;
            case 'audio':
                return `${baseUrl}/uploads/audio/${filename}`;
            default:
                return null;
        }
    }

    // static async findQuestionsByLevelAndStrength(userId, level, strength) {
    //     try {
    //         // Reset old asked questions (older than 24 hours)
    //         await AskedQuestion.resetAskedQuestions(userId);

    //         // Get list of already asked questions
    //         const askedQuestionIds = await AskedQuestion.getAskedQuestions(userId);

    //         // First get the questions
    //         const query = `
    //             SELECT 
    //                 q.*,
    //                 qc.name as category_name,
    //                 qc.description as category_description
    //             FROM tbl_questions q
    //             LEFT JOIN tbl_questions_categories qc ON q.question_category_id = qc.id
    //             WHERE q.strength = ?
    //             ${askedQuestionIds.length > 0 ? 'AND q.id NOT IN (?)' : ''}
    //             ORDER BY RAND()
                
    //         `;

    //         const queryParams = askedQuestionIds.length > 0 
    //             ? [strength, askedQuestionIds]
    //             : [strength];

    //         const [questions] = await pool.query(query, queryParams);

    //         // If no questions found, return empty array
    //         if (questions.length === 0) {
    //             return [];
    //         }

    //         // Get choices for all questions
    //         const questionIds = questions.map(q => q.id);
    //         const choicesQuery = `
    //             SELECT * 
    //             FROM tbl_questions_choices 
    //             WHERE qId IN (?)
    //             ORDER BY id ASC
    //         `;
    //         const [allChoices] = await pool.query(choicesQuery, [questionIds]);

    //         // Mark these questions as asked
           

    //         // Map choices to questions and format file paths
    //         return questions.map(question => {
    //             // Get choices for this specific question
    //             const questionChoices = allChoices.filter(choice => choice.qId === question.id);

    //             return new Question({
    //                 ...question,
    //                 choices: questionChoices || [],
    //                 category: {
    //                     id: question.question_category_id,
    //                     name: question.category_name || '',
    //                     description: question.category_description || ''
    //                 }
    //             });
    //         });
    //     } catch (error) {
    //         console.error('Error in findQuestionsByLevelAndStrength:', error);
    //         throw error;
    //     }
    // }

    static async findQuestionsByLevelAndStrength(userId, level, strength) {
        try {
            // Reset old asked questions (older than 24 hours)
            await AskedQuestion.resetAskedQuestions(userId);
    
            // Get list of already asked questions
            const askedQuestionIds = await AskedQuestion.getAskedQuestions(userId);
    
            // Step 1: Get user's interested categories
            const [userInterestRows] = await pool.query(
                'SELECT categories FROM tbl_user_interests WHERE userId = ? ORDER BY updatedAt DESC LIMIT 1',
                [userId]
            );
    
            if (!userInterestRows.length) {
                throw new AppError('User interests not found.', 400);
            }
    
            const interestedCategoriesRaw = userInterestRows[0].categories;
            const interestedCategories = typeof interestedCategoriesRaw === 'string' ? JSON.parse(interestedCategoriesRaw) : interestedCategoriesRaw;
    
            if (!interestedCategories.length) {
                throw new AppError('User has no interested categories selected.', 400);
            }
    
            // Step 2: Build the dynamic query
            const questionBaseQuery = `
                SELECT 
                    q.*,
                    qc.name as category_name,
                    qc.description as category_description
                FROM tbl_questions q
                LEFT JOIN tbl_questions_categories qc ON q.question_category_id = qc.id
                WHERE q.strength = ?
                  AND q.question_category_id IN (?)
                  ${askedQuestionIds.length > 0 ? 'AND q.id NOT IN (?)' : ''}
                ORDER BY RAND()
            `;
    
            const queryParams = askedQuestionIds.length > 0
                ? [strength, interestedCategories, askedQuestionIds]
                : [strength, interestedCategories];
    
            const [questions] = await pool.query(questionBaseQuery, queryParams);
    
            if (questions.length === 0) {
                return [];
            }
    
            // Step 3: Get choices for all questions
            const questionIds = questions.map(q => q.id);
            const choicesQuery = `
                SELECT * 
                FROM tbl_questions_choices 
                WHERE qId IN (?)
                ORDER BY id ASC
            `;
            const [allChoices] = await pool.query(choicesQuery, [questionIds]);
    
            // Step 4: Map choices to questions and return
            return questions.map(question => {
                const questionChoices = allChoices.filter(choice => choice.qId === question.id);
                return new Question({
                    ...question,
                    choices: questionChoices || [],
                    category: {
                        id: question.question_category_id,
                        name: question.category_name || '',
                        description: question.category_description || ''
                    }
                });
            });
        } catch (error) {
            console.error('Error in findQuestionsByLevelAndStrength:', error);
            throw error;
        }
    }
    

    static async getQuestionById(id) {
        try {
            const query = `
                SELECT 
                    q.*,
                    qc.name as category_name,
                    qc.description as category_description
                FROM tbl_questions q
                LEFT JOIN tbl_questions_categories qc ON q.question_category_id = qc.id
                WHERE q.id = ?
            `;

            const [questions] = await pool.query(query, [id]);

            if (!questions[0]) return null;

            // Get choices
            const choicesQuery = 'SELECT * FROM tbl_questions_choices WHERE qId = ? ORDER BY id ASC';
            const [choices] = await pool.query(choicesQuery, [id]);

            return new Question({
                ...questions[0],
                choices: choices || [],
                category: {
                    id: questions[0].question_category_id,
                    name: questions[0].category_name || '',
                    description: questions[0].category_description || ''
                }
            });
        } catch (error) {
            console.error('Error in getQuestionById:', error);
            throw error;
        }
    }

    

    static async updateLevel(userId,challenge_id ,level) {
        const query = 'UPDATE tbl_challenge_user_mapping SET level = ? WHERE challenge_id = ? And userId = ?';
        await pool.query(query, [level,challenge_id ,userId]);
    }

    


    static async findQuestionsByType(type) {
        try {
            const query = `
                SELECT 
                    q.*,
                    qc.name as category_name,
                    qc.description as category_description
                FROM tbl_questions q
                LEFT JOIN tbl_questions_categories qc ON q.question_category_id = qc.id
                WHERE q.type = ?
            `;

            const [questions] = await pool.query(query, [type]);
            
            // If no questions found, return empty array
            if (questions.length === 0) {
                return [];
            }

            // Get choices for all questions
            const questionIds = questions.map(q => q.id);
            const choicesQuery = `
                SELECT * 
                FROM tbl_questions_choices 
                WHERE qId IN (?)
                ORDER BY id ASC
            `;
            const [allChoices] = await pool.query(choicesQuery, [questionIds]);

            // Map choices to questions and format file paths
            return questions.map(question => {
                // Get choices for this specific question
                const questionChoices = allChoices.filter(choice => choice.qId === question.id);

                return new Question({
                    ...question,
                    choices: questionChoices || [],
                    category: {
                        id: question.question_category_id,
                        name: question.category_name || '',
                        description: question.category_description || ''
                    }
                });
            });
        } catch (error) {
            console.error('Error in findQuestionsByType:', error);
            throw error;
        }
    }
}

module.exports = Question; 