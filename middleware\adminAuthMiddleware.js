const jwt = require('jsonwebtoken');
const { AppError } = require('./errorMiddleware');

const authenticateAdmin = (req, res, next) => {
    try {
        const authHeader = req.headers.authorization;
        
        if (!authHeader) {
            throw new AppError('Access token is required', 401);
        }

        const token = authHeader.split(' ')[1]; // Bearer TOKEN
        
        if (!token) {
            throw new AppError('Access token is required', 401);
        }

        const decoded = jwt.verify(token, process.env.JWT_SECRET);
        
        // Check if the token is for an admin user
        if (decoded.type !== 'admin') {
            throw new AppError('Admin access required', 403);
        }

        req.user = decoded;
        next();
    } catch (error) {
        if (error.name === 'JsonWebTokenError') {
            next(new AppError('Invalid token', 401));
        } else if (error.name === 'TokenExpiredError') {
            next(new AppError('Token expired', 401));
        } else {
            next(error);
        }
    }
};

module.exports = authenticateAdmin;
