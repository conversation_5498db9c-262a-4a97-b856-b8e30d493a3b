const { Server } = require('socket.io');
const jwt = require('jsonwebtoken');
const { pool } = require('../config/config');
const leaderBoardModel = require('../models/leaderboardModel');
const ChallengeUserMappingModel = require('../models/challengeUserMappingModel');
const SocketManager = require('../config/socket');

// Mock dependencies
jest.mock('socket.io');
jest.mock('jsonwebtoken');
jest.mock('../config/config');
jest.mock('../models/leaderboardModel');

// We'll use the real ChallengeUserMappingModel but mock specific methods
jest.mock('../models/challengeUserMappingModel', () => {
  const originalModule = jest.requireActual('../models/challengeUserMappingModel');
  return {
    ...originalModule,
    updateRanks: jest.fn(),
    updateProgress: jest.fn()
  };
});

describe('Socket Rank Integration Tests', () => {
  let socketManager;
  let mockServer;
  let mockSocket;
  let mockIo;
  let mockClient;

  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();

    // Use fake timers
    jest.useFakeTimers();

    // Mock socket.io Server and Client
    mockIo = {
      use: jest.fn(),
      on: jest.fn(),
      to: jest.fn().mockReturnThis(),
      emit: jest.fn(),
      sockets: {
        adapter: {
          rooms: new Map()
        }
      }
    };
    Server.mockImplementation(() => mockIo);

    // Mock socket
    mockSocket = {
      id: 'socket-id-123',
      join: jest.fn(),
      on: jest.fn(),
      emit: jest.fn(),
      onAny: jest.fn()
    };

    // Mock server
    mockServer = {};

    // Create SocketManager instance
    socketManager = new SocketManager(mockServer);
  });

  afterEach(() => {
    // Clean up any intervals that might have been created
    if (socketManager) {
      // Call cleanup method to clear all intervals
      if (typeof socketManager.cleanup === 'function') {
        socketManager.cleanup();
      }

      // Clear all intervals manually as a fallback
      for (const [key, intervalId] of socketManager.updateIntervals.entries()) {
        clearInterval(intervalId);
      }
      socketManager.updateIntervals.clear();

      for (const [key, intervalId] of socketManager.leaderboardUpdateIntervals.entries()) {
        clearInterval(intervalId);
      }
      socketManager.leaderboardUpdateIntervals.clear();
    }

    // Restore real timers
    jest.useRealTimers();
  });

  describe('Rank updates in socket connections', () => {
    test('should update ranks when user joins a challenge', async () => {
      // Arrange
      const token = 'valid-token';
      const challengeId = 123;
      const userId = 456;

      // Mock JWT verification
      jwt.verify.mockReturnValue({ id: userId });

      // Mock database query results
      pool.query.mockResolvedValueOnce([[]]);  // No existing game state

      // Mock updateRanks method
      ChallengeUserMappingModel.updateRanks.mockResolvedValue([
        { id: 1, userId: 456, score: 100, rank: 1 },
        { id: 2, userId: 789, score: 50, rank: 2 }
      ]);

      // Mock leaderboard data
      const mockLeaderboardData = [
        { rank: 1, userId: 456, fullName: 'User 1', score: 100 },
        { rank: 2, userId: 789, fullName: 'User 2', score: 50 }
      ];
      leaderBoardModel.getChallengeLeaderboardData.mockResolvedValue(mockLeaderboardData);

      // Simulate the 'connection' event
      const connectionHandler = mockIo.on.mock.calls[0][1];
      await connectionHandler(mockSocket);

      // Get the joinGame event handler
      const eventHandlers = mockSocket.on.mock.calls;
      const joinGameEventHandler = eventHandlers.find(call => call[0] === 'joinGame')[1];

      // Act
      await joinGameEventHandler({ token, challengeId });

      // Assert
      expect(ChallengeUserMappingModel.updateRanks).toHaveBeenCalledWith(challengeId);
      expect(leaderBoardModel.getChallengeLeaderboardData).toHaveBeenCalledWith(challengeId, 10);
      expect(mockSocket.emit).toHaveBeenCalledWith('gameleaderboardData', { leaderboardData: mockLeaderboardData });
    });

    test('should update ranks when user score changes', async () => {
      // Arrange
      const userId = 456;
      const challengeId = 123;
      const scoreIncrease = 50;

      // Create a custom implementation of updateProgress that calls updateRanks
      ChallengeUserMappingModel.updateProgress = jest.fn().mockImplementation(async (userId, challengeId, scoreIncrease) => {
        // Call updateRanks as part of the implementation
        await ChallengeUserMappingModel.updateRanks(challengeId);

        // Return mock result
        return {
          level: 2,
          score: 150,
          life: 3,
          status: 1,
          rank: 1 // Updated rank
        };
      });

      // Mock updateRanks method
      ChallengeUserMappingModel.updateRanks.mockResolvedValue([
        { id: 1, userId: 456, score: 150, rank: 1 },
        { id: 2, userId: 789, score: 100, rank: 2 }
      ]);

      // Act - simulate a score update
      const result = await ChallengeUserMappingModel.updateProgress(userId, challengeId, scoreIncrease);

      // Assert
      expect(result.rank).toBe(1); // Rank should be updated
      expect(ChallengeUserMappingModel.updateRanks).toHaveBeenCalledWith(challengeId);
    });

    test('should update ranks during periodic leaderboard updates', async () => {
      // Arrange
      const roomKey = 'challenge-123';
      const challengeId = 123;

      // Save the original method
      const originalMethod = socketManager.startPeriodicLeaderboardUpdates;

      // Create a mock implementation of startPeriodicLeaderboardUpdates
      socketManager.startPeriodicLeaderboardUpdates = jest.fn().mockImplementation(async (roomKey, challengeId) => {
        // Simulate what the real method would do
        await ChallengeUserMappingModel.updateRanks(challengeId);
        const leaderboardData = await leaderBoardModel.getChallengeLeaderboardData(challengeId, 10);
        mockIo.to(roomKey).emit('gameleaderboardData', { leaderboardData });
        return 999; // Return a fake interval ID
      });

      // Mock updateRanks method
      ChallengeUserMappingModel.updateRanks.mockResolvedValue([
        { id: 1, userId: 456, score: 150, rank: 1 },
        { id: 2, userId: 789, score: 100, rank: 2 }
      ]);

      // Mock leaderboard data
      const mockLeaderboardData = [
        { rank: 1, userId: 456, fullName: 'User 1', score: 150 },
        { rank: 2, userId: 789, fullName: 'User 2', score: 100 }
      ];
      leaderBoardModel.getChallengeLeaderboardData.mockResolvedValue(mockLeaderboardData);

      // Act
      await socketManager.startPeriodicLeaderboardUpdates(roomKey, challengeId);

      // Assert
      expect(socketManager.startPeriodicLeaderboardUpdates).toHaveBeenCalledWith(roomKey, challengeId);
      expect(ChallengeUserMappingModel.updateRanks).toHaveBeenCalledWith(challengeId);
      expect(leaderBoardModel.getChallengeLeaderboardData).toHaveBeenCalledWith(challengeId, 10);
      expect(mockIo.to).toHaveBeenCalledWith(roomKey);
      expect(mockIo.emit).toHaveBeenCalledWith('gameleaderboardData', { leaderboardData: mockLeaderboardData });

      // Restore the original method
      socketManager.startPeriodicLeaderboardUpdates = originalMethod;
    });

    test('should update ranks on manual leaderboard refresh', async () => {
      // Arrange
      const token = 'valid-token';
      const challengeId = 123;
      const userId = 456;

      // Mock JWT verification
      jwt.verify.mockReturnValue({ id: userId });

      // Mock updateRanks method
      ChallengeUserMappingModel.updateRanks.mockResolvedValue([
        { id: 1, userId: 456, score: 150, rank: 1 },
        { id: 2, userId: 789, score: 100, rank: 2 }
      ]);

      // Mock leaderboard data
      const mockLeaderboardData = [
        { rank: 1, userId: 456, fullName: 'User 1', score: 150 },
        { rank: 2, userId: 789, fullName: 'User 2', score: 100 }
      ];
      leaderBoardModel.getChallengeLeaderboardData.mockResolvedValue(mockLeaderboardData);

      // Simulate the 'connection' event
      const connectionHandler = mockIo.on.mock.calls[0][1];
      await connectionHandler(mockSocket);

      // Get the leaderboard event handler
      const eventHandlers = mockSocket.on.mock.calls;
      const leaderboardEventHandler = eventHandlers.find(call => call[0] === 'leaderboard')[1];

      // Act
      await leaderboardEventHandler({ token, challengeId });

      // Assert
      expect(ChallengeUserMappingModel.updateRanks).toHaveBeenCalledWith(challengeId);
      expect(leaderBoardModel.getChallengeLeaderboardData).toHaveBeenCalledWith(challengeId, 10);
      expect(mockSocket.emit).toHaveBeenCalledWith('gameleaderboardData', { leaderboardData: mockLeaderboardData });
    });

    test('should include rank in gameState updates', async () => {
      // Arrange
      const userId = 456;
      const challengeId = 123;
      const userGameKey = `${userId}-${challengeId}`;

      // Mock game state with rank
      const mockGameState = {
        score: 100,
        life: 3,
        level: 2,
        max_life: 5,
        rank: 1
      };

      // Set up active game
      socketManager.activeGames.set(userGameKey, {
        socket: mockSocket,
        userId,
        challengeId,
        gameState: null // Initial state is null
      });

      // Save the original method
      const originalMethod = socketManager.startPeriodicUpdates;

      // Create a mock implementation of startPeriodicUpdates
      socketManager.startPeriodicUpdates = jest.fn().mockImplementation((userGameKey) => {
        const game = socketManager.activeGames.get(userGameKey);
        if (game) {
          // Simulate what the real method would do
          game.gameState = mockGameState;
          game.socket.emit('gameState', {
            score: mockGameState.score,
            life: mockGameState.life,
            level: mockGameState.level,
            maxLife: mockGameState.max_life,
            rank: mockGameState.rank
          });
        }
        return 999; // Return a fake interval ID
      });

      // Mock the database query
      pool.query.mockResolvedValueOnce([[mockGameState]]);

      // Act
      const result = socketManager.startPeriodicUpdates(userGameKey);

      // Assert
      expect(socketManager.startPeriodicUpdates).toHaveBeenCalledWith(userGameKey);
      expect(mockSocket.emit).toHaveBeenCalledWith('gameState', {
        score: mockGameState.score,
        life: mockGameState.life,
        level: mockGameState.level,
        maxLife: mockGameState.max_life,
        rank: mockGameState.rank
      });

      // Restore the original method
      socketManager.startPeriodicUpdates = originalMethod;
    });
  });
});
