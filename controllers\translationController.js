const TranslationService = require('../services/translationService');
const { AppError } = require('../middleware/errorMiddleware');

class TranslationController {
    // Challenge Translations
    static async getChallengeTranslations(req, res, next) {
        try {
            const { challengeId } = req.params;
            const translations = await TranslationService.getChallengeTranslations(challengeId);
            res.success(translations, 'Challenge translations retrieved successfully');
        } catch (error) {
            next(error);
        }
    }

    static async getChallengeTranslation(req, res, next) {
        try {
            const { challengeId, langId } = req.params;
            const translation = await TranslationService.getChallengeTranslation(challengeId, langId);
            
            if (!translation) {
                throw new AppError('Challenge translation not found', 404);
            }
            
            res.success(translation, 'Challenge translation retrieved successfully');
        } catch (error) {
            next(error);
        }
    }

    static async createChallengeTranslation(req, res, next) {
        try {
            const { challengeId } = req.params;
            const { name, description, langId } = req.body;

            if (!name || !langId) {
                throw new AppError('name and langId are required', 400);
            }

            const translation = await TranslationService.createChallengeTranslation({
                challengeId,
                name,
                description,
                langId
            });

            res.success(translation, 'Challenge translation created successfully');
        } catch (error) {
            next(error);
        }
    }

    static async updateChallengeTranslation(req, res, next) {
        try {
            const { challengeId, langId } = req.params;
            const { name, description } = req.body;

            if (!name) {
                throw new AppError('name is required', 400);
            }

            const translation = await TranslationService.updateChallengeTranslation(
                challengeId, 
                langId, 
                { name, description }
            );

            res.success(translation, 'Challenge translation updated successfully');
        } catch (error) {
            next(error);
        }
    }

    static async deleteChallengeTranslation(req, res, next) {
        try {
            const { translationId } = req.params;
            const result = await TranslationService.deleteChallengeTranslation(translationId);
            res.success(result, 'Challenge translation deleted successfully');
        } catch (error) {
            next(error);
        }
    }

    // Question Translations
    static async getQuestionTranslations(req, res, next) {
        try {
            const { questionId } = req.params;
            const translations = await TranslationService.getQuestionTranslations(questionId);
            res.success(translations, 'Question translations retrieved successfully');
        } catch (error) {
            next(error);
        }
    }

    static async getQuestionTranslation(req, res, next) {
        try {
            const { questionId, langId } = req.params;
            const translation = await TranslationService.getQuestionTranslation(questionId, langId);
            
            if (!translation) {
                throw new AppError('Question translation not found', 404);
            }
            
            res.success(translation, 'Question translation retrieved successfully');
        } catch (error) {
            next(error);
        }
    }

    static async createQuestionTranslation(req, res, next) {
        try {
            const { questionId } = req.params;
            const { question, langId } = req.body;

            if (!question || !langId) {
                throw new AppError('question and langId are required', 400);
            }

            const translation = await TranslationService.createQuestionTranslation({
                questionId,
                question,
                langId
            });

            res.success(translation, 'Question translation created successfully');
        } catch (error) {
            next(error);
        }
    }

    static async updateQuestionTranslation(req, res, next) {
        try {
            const { questionId, langId } = req.params;
            const { question } = req.body;

            if (!question) {
                throw new AppError('question is required', 400);
            }

            const translation = await TranslationService.updateQuestionTranslation(
                questionId, 
                langId, 
                { question }
            );

            res.success(translation, 'Question translation updated successfully');
        } catch (error) {
            next(error);
        }
    }

    // Question Choice Translations
    static async getQuestionChoiceTranslations(req, res, next) {
        try {
            const { choiceId } = req.params;
            const translations = await TranslationService.getQuestionChoiceTranslations(choiceId);
            res.success(translations, 'Choice translations retrieved successfully');
        } catch (error) {
            next(error);
        }
    }

    static async getQuestionChoicesWithTranslations(req, res, next) {
        try {
            const { questionId } = req.params;
            const { langId } = req.query;
            const choices = await TranslationService.getQuestionChoicesWithTranslations(questionId, langId);
            res.success(choices, 'Question choices with translations retrieved successfully');
        } catch (error) {
            next(error);
        }
    }

    static async createQuestionChoiceTranslation(req, res, next) {
        try {
            const { choiceId } = req.params;
            const { choice, langId } = req.body;

            if (!choice || !langId) {
                throw new AppError('choice and langId are required', 400);
            }

            const translation = await TranslationService.createQuestionChoiceTranslation({
                choiceId,
                choice,
                langId
            });

            res.success(translation, 'Choice translation created successfully');
        } catch (error) {
            next(error);
        }
    }

    static async updateQuestionChoiceTranslation(req, res, next) {
        try {
            const { choiceId, langId } = req.params;
            const { choice } = req.body;

            if (!choice) {
                throw new AppError('choice is required', 400);
            }

            const translation = await TranslationService.updateQuestionChoiceTranslation(
                choiceId, 
                langId, 
                { choice }
            );

            res.success(translation, 'Choice translation updated successfully');
        } catch (error) {
            next(error);
        }
    }

    // Question Category Translations
    static async getQuestionCategoryTranslations(req, res, next) {
        try {
            const { langId } = req.query;
            const translations = await TranslationService.getQuestionCategoryTranslations(langId);
            res.success(translations, 'Category translations retrieved successfully');
        } catch (error) {
            next(error);
        }
    }

    static async createQuestionCategoryTranslation(req, res, next) {
        try {
            const { name, langId, category_icon_id } = req.body;

            if (!name || !langId || !category_icon_id) {
                throw new AppError('name, langId, and category_icon_id are required', 400);
            }

            const translation = await TranslationService.createQuestionCategoryTranslation({
                name,
                langId,
                category_icon_id
            });

            res.success(translation, 'Category translation created successfully');
        } catch (error) {
            next(error);
        }
    }

    static async updateQuestionCategoryTranslation(req, res, next) {
        try {
            const { categoryIconId, langId } = req.params;
            const { name } = req.body;

            if (!name) {
                throw new AppError('name is required', 400);
            }

            const translation = await TranslationService.updateQuestionCategoryTranslation(
                categoryIconId, 
                langId, 
                { name }
            );

            res.success(translation, 'Category translation updated successfully');
        } catch (error) {
            next(error);
        }
    }

    // Bulk operations
    static async getChallengesWithTranslations(req, res, next) {
        try {
            const { langId } = req.query;
            const limit = parseInt(req.query.limit) || null;
            const challenges = await TranslationService.getChallengesWithTranslations(langId, limit);
            res.success(challenges, 'Challenges with translations retrieved successfully');
        } catch (error) {
            next(error);
        }
    }

    static async getQuestionsWithTranslations(req, res, next) {
        try {
            const { langId } = req.query;
            const limit = parseInt(req.query.limit) || null;
            const questions = await TranslationService.getQuestionsWithTranslations(langId, limit);
            res.success(questions, 'Questions with translations retrieved successfully');
        } catch (error) {
            next(error);
        }
    }
}

module.exports = TranslationController;
