const { pool } = require('../config/config');
const ChallengeUserMappingModel = require('../models/challengeUserMappingModel');

// Mock the database connection
jest.mock('../config/config', () => ({
  pool: {
    query: jest.fn(),
    getConnection: jest.fn()
  }
}));

describe('ChallengeUserMappingModel - endChallenge', () => {
  let mockConnection;

  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();

    // Mock the database connection for transaction
    mockConnection = {
      beginTransaction: jest.fn().mockResolvedValue(undefined),
      query: jest.fn(),
      commit: jest.fn().mockResolvedValue(undefined),
      rollback: jest.fn().mockResolvedValue(undefined),
      release: jest.fn()
    };

    // Mock getConnection to return our mock connection
    pool.getConnection.mockResolvedValue(mockConnection);
  });

  test('should update ranks when ending a challenge', async () => {
    // Arrange
    const challengeId = 123;
    const userId = 456;

    // Mock the update queries
    mockConnection.query.mockResolvedValueOnce([{ affectedRows: 1 }]); // Update challenge status
    mockConnection.query.mockResolvedValueOnce([{ affectedRows: 1 }]); // Update mapping status

    // Mock the updateRanks method
    const originalUpdateRanks = ChallengeUserMappingModel.updateRanks;
    ChallengeUserMappingModel.updateRanks = jest.fn().mockResolvedValue([
      { id: 1, userId: 456, score: 500, rank: 1 },
      { id: 2, userId: 789, score: 300, rank: 2 }
    ]);

    // Mock the final state query
    const mockFinalState = [{
      id: 1,
      userId: 456,
      challenge_id: 123,
      score: 500,
      level: 5,
      rank: 1, // Rank should be included in the final state
      status: 2,
      life: 3,
      max_life: 5,
      winning_rules: JSON.stringify({
        level: 5,
        winning_points: 500,
        rank: 1
      })
    }];
    pool.query.mockResolvedValueOnce([mockFinalState]);

    // Mock the checkWinner method
    const originalCheckWinner = ChallengeUserMappingModel.checkWinner;
    ChallengeUserMappingModel.checkWinner = jest.fn().mockResolvedValue({
      isWinner: true,
      winningRules: {
        level: 5,
        winning_points: 500,
        rank: 1
      }
    });

    // Mock the addWinner method
    const originalAddWinner = ChallengeUserMappingModel.addWinner;
    ChallengeUserMappingModel.addWinner = jest.fn().mockResolvedValue({
      success: true,
      message: 'User added as winner successfully',
      isNewWinner: true,
      winnerId: 999
    });

    // Act
    const result = await ChallengeUserMappingModel.endChallenge(challengeId, userId);

    // Assert
    // Should start a transaction
    expect(mockConnection.beginTransaction).toHaveBeenCalled();

    // Should update challenge status
    expect(mockConnection.query).toHaveBeenNthCalledWith(
      1,
      expect.stringContaining('UPDATE tbl_challenges'),
      [challengeId]
    );

    // Should update mapping status
    expect(mockConnection.query).toHaveBeenNthCalledWith(
      2,
      expect.stringContaining('UPDATE tbl_challenge_user_mapping'),
      [challengeId, userId]
    );

    // Should commit the transaction
    expect(mockConnection.commit).toHaveBeenCalled();

    // Should call updateRanks
    expect(ChallengeUserMappingModel.updateRanks).toHaveBeenCalledWith(challengeId);

    // Should get the final state
    expect(pool.query).toHaveBeenCalledWith(
      expect.stringContaining('SELECT m.*, c.user_life as max_life, c.winning_rules'),
      [userId, challengeId]
    );

    // Should check if the user is a winner
    expect(ChallengeUserMappingModel.checkWinner).toHaveBeenCalledWith(challengeId, userId);

    // Should add the user as a winner
    expect(ChallengeUserMappingModel.addWinner).toHaveBeenCalledWith(challengeId, userId);

    // Should return the correct result with rank included in finalState
    expect(result).toEqual({
      success: true,
      message: 'Challenge ended successfully',
      finalState: mockFinalState[0],
      isWinner: true,
      winningRules: {
        level: 5,
        winning_points: 500,
        rank: 1
      },
      winnerAddResult: {
        success: true,
        message: 'User added as winner successfully',
        isNewWinner: true,
        winnerId: 999
      }
    });

    // Verify that the rank is included in the final state
    expect(result.finalState.rank).toBe(1);

    // Restore original methods
    ChallengeUserMappingModel.updateRanks = originalUpdateRanks;
    ChallengeUserMappingModel.checkWinner = originalCheckWinner;
    ChallengeUserMappingModel.addWinner = originalAddWinner;
  });
});
