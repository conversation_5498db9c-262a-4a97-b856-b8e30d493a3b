const successResponse = (req, res, next) => {
    res.success = (data, message = 'Success', statusCode = 200) => {
        return res.status(statusCode).json({
            status: 'success',
            message,
            data
        });
    };

    res.created = (data, message = 'Created successfully') => {
        return res.status(201).json({
            status: 'success',
            message,
            data
        });
    };

    res.updated = (data, message = 'Updated successfully') => {
        return res.status(200).json({
            status: 'success',
            message,
            data
        });
    };

    res.deleted = (message = 'Deleted successfully') => {
        return res.status(200).json({
            status: 'success',
            message
        });
    };

    res.noContent = () => {
        return res.status(204).send();
    };

    next();
};

module.exports = successResponse;
