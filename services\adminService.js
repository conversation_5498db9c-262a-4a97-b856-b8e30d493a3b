const UserAdmin = require('../models/usersAdminModel');
const { AppError } = require('../middleware/errorMiddleware');

class AdminService {
    static async login(username, password) {
        try {
            // Find admin by username
            const admin = await UserAdmin.findByUsername(username);
            if (!admin) {
                throw new AppError('Invalid credentials', 401);
            }

            // Verify password
            const isValid = await admin.verifyPassword(password);
            if (!isValid) {
                throw new AppError('Invalid credentials', 401);
            }

            // Generate token
            const token = admin.generateToken();

            return {
                admin: {
                    id: admin.id,
                    fullName: admin.fullName,
                    username: admin.username,
                    createdAt: admin.createdAt
                },
                token
            };
        } catch (error) {
            if (error instanceof AppError) {
                throw error;
            }
            throw new AppError('Login failed', 500);
        }
    }

    static async getAllAdmins() {
        try {
            return await UserAdmin.findAll();
        } catch (error) {
            throw new AppError('Error fetching admins', 500);
        }
    }

    static async getAdminById(id) {
        try {
            const admin = await UserAdmin.findById(id);
            if (!admin) {
                throw new AppError('Admin not found', 404);
            }
            
            // Remove password from response
            const { password, ...adminData } = admin;
            return adminData;
        } catch (error) {
            if (error instanceof AppError) {
                throw error;
            }
            throw new AppError('Error fetching admin', 500);
        }
    }

    static async createAdmin(adminData) {
        try {
            // Check if username already exists
            const existingAdmin = await UserAdmin.findByUsername(adminData.username);
            if (existingAdmin) {
                throw new AppError('Username already exists', 400);
            }

            const admin = await UserAdmin.create(adminData);
            
            // Remove password from response
            const { password, ...adminResponse } = admin;
            return adminResponse;
        } catch (error) {
            if (error instanceof AppError) {
                throw error;
            }
            throw new AppError('Error creating admin', 500);
        }
    }

    static async updateAdmin(id, adminData) {
        try {
            const existingAdmin = await UserAdmin.findById(id);
            if (!existingAdmin) {
                throw new AppError('Admin not found', 404);
            }

            // Check if username is being changed and if it already exists
            if (adminData.username && adminData.username !== existingAdmin.username) {
                const usernameExists = await UserAdmin.findByUsername(adminData.username);
                if (usernameExists) {
                    throw new AppError('Username already exists', 400);
                }
            }

            const updatedAdmin = await UserAdmin.update(id, adminData);
            
            // Remove password from response
            const { password, ...adminResponse } = updatedAdmin;
            return adminResponse;
        } catch (error) {
            if (error instanceof AppError) {
                throw error;
            }
            throw new AppError('Error updating admin', 500);
        }
    }

    static async deleteAdmin(id) {
        try {
            const admin = await UserAdmin.findById(id);
            if (!admin) {
                throw new AppError('Admin not found', 404);
            }

            const deleted = await UserAdmin.delete(id);
            if (!deleted) {
                throw new AppError('Failed to delete admin', 500);
            }

            return { message: 'Admin deleted successfully' };
        } catch (error) {
            if (error instanceof AppError) {
                throw error;
            }
            throw new AppError('Error deleting admin', 500);
        }
    }
}

module.exports = AdminService;
