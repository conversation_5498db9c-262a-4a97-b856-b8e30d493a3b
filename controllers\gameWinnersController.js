const GameWinnersService = require('../services/gameWinnersService');
const { AppError } = require('../middleware/errorMiddleware');

class GameWinnersController {
    static async getAllWinners(req, res, next) {
        try {
            const limit = parseInt(req.query.limit) || 10;
            const winners = await GameWinnersService.getAllWinners(limit);
            res.success(winners, 'Game winners retrieved successfully');
        } catch (error) {
            next(error);
        }
    }

    static async getWinnersByChallenge(req, res, next) {
        try {
            const { challengeId } = req.params;
            const limit = parseInt(req.query.limit) || 10;
            const winners = await GameWinnersService.getWinnersByChallenge(challengeId, limit);
            res.success(winners, 'Challenge winners retrieved successfully');
        } catch (error) {
            next(error);
        }
    }

    static async getWinnersByUser(req, res, next) {
        try {
            const { userId } = req.params;
            const winners = await GameWinnersService.getWinnersByUser(userId);
            res.success(winners, 'User wins retrieved successfully');
        } catch (error) {
            next(error);
        }
    }

    static async getUserWins(req, res, next) {
        try {
            const userId = req.user.id;
            const winners = await GameWinnersService.getWinnersByUser(userId);
            res.success(winners, 'Your wins retrieved successfully');
        } catch (error) {
            next(error);
        }
    }

    static async createWinner(req, res, next) {
        try {
            const { userId, challengeId } = req.body;
            
            if (!userId || !challengeId) {
                throw new AppError('userId and challengeId are required', 400);
            }

            const winner = await GameWinnersService.createWinner({ userId, challengeId });
            res.success(winner, 'Game winner created successfully');
        } catch (error) {
            next(error);
        }
    }

    static async deleteWinner(req, res, next) {
        try {
            const { winnerId } = req.params;
            const result = await GameWinnersService.deleteWinner(winnerId);
            res.success(result, 'Game winner deleted successfully');
        } catch (error) {
            next(error);
        }
    }

    static async checkWinnerStatus(req, res, next) {
        try {
            const { userId, challengeId } = req.params;
            const isWinner = await GameWinnersService.checkWinnerExists(userId, challengeId);
            res.success({ isWinner }, 'Winner status checked successfully');
        } catch (error) {
            next(error);
        }
    }

    static async checkMyWinnerStatus(req, res, next) {
        try {
            const userId = req.user.id;
            const { challengeId } = req.params;
            const isWinner = await GameWinnersService.checkWinnerExists(userId, challengeId);
            res.success({ isWinner }, 'Your winner status checked successfully');
        } catch (error) {
            next(error);
        }
    }
}

module.exports = GameWinnersController;
