const { pool } = require('../config/config');

class ChallengeCategoriesIcon {
    constructor(icon) {
        this.id = icon.id;
        this.icon = icon.icon;
        this.color = icon.color;
    }

    static async findAll() {
        const [rows] = await pool.query('SELECT * FROM tbl_challenge_categories_icons ORDER BY id ASC');
        return rows.map(row => new ChallengeCategoriesIcon(row));
    }

    static async findById(id) {
        const [rows] = await pool.query('SELECT * FROM tbl_challenge_categories_icons WHERE id = ?', [id]);
        return rows[0] ? new ChallengeCategoriesIcon(rows[0]) : null;
    }

    static async create(iconData) {
        const [result] = await pool.query(
            'INSERT INTO tbl_challenge_categories_icons (icon, color) VALUES (?, ?)',
            [iconData.icon, iconData.color]
        );
        return this.findById(result.insertId);
    }

    static async update(id, iconData) {
        await pool.query(
            'UPDATE tbl_challenge_categories_icons SET icon = ?, color = ? WHERE id = ?',
            [iconData.icon, iconData.color, id]
        );
        return this.findById(id);
    }

    static async delete(id) {
        const [result] = await pool.query('DELETE FROM tbl_challenge_categories_icons WHERE id = ?', [id]);
        return result.affectedRows > 0;
    }
}

module.exports = ChallengeCategoriesIcon;
