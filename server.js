require('dotenv').config(); // Load environment variables
const app = require('./app');
const { testConnection } = require('./config/config');
const SocketManager = require('./config/socket');
const GameEndScheduler = require('./scheduler/gameEndScheduler');
const DatabaseMigrationService = require('./services/databaseMigrationService');

// Global variables to hold instances
let socketManager = null;
let gameEndScheduler = null;

/**
 * Graceful shutdown function
 * Cleans up resources before shutting down
 */
const gracefulShutdown = (server, exitCode = 0) => {
    console.log('Starting graceful shutdown...');

    // Clean up socket resources if they exist
    if (socketManager) {
        console.log('Cleaning up socket resources...');
        socketManager.cleanup();
    }

    // Stop the game end scheduler if it exists
    if (gameEndScheduler) {
        console.log('Stopping game end scheduler...');
        gameEndScheduler.stop();
    }

    // Close the server
    if (server) {
        console.log('Closing HTTP server...');
        server.close(() => {
            console.log('HTTP server closed.');
            process.exit(exitCode);
        });

        // Force close after timeout if it's taking too long
        setTimeout(() => {
            console.error('Could not close connections in time, forcefully shutting down');
            process.exit(exitCode);
        }, 10000);
    } else {
        process.exit(exitCode);
    }
};

// Handle uncaught exceptions
process.on('uncaughtException', (err) => {
    console.error('UNCAUGHT EXCEPTION! 💥 Shutting down...');
    console.error(err.name, err.message);
    gracefulShutdown(null, 1);
});

// Handle SIGTERM signal (e.g., from Kubernetes)
process.on('SIGTERM', () => {
    console.log('SIGTERM received. Shutting down gracefully...');
    gracefulShutdown(null, 0);
});

// Handle SIGINT signal (e.g., Ctrl+C)
process.on('SIGINT', () => {
    console.log('SIGINT received. Shutting down gracefully...');
    gracefulShutdown(null, 0);
});

// Server configuration
const PORT = process.env.PORT || 3000;

// Test database connection before starting the server
const startServer = async () => {
    try {
        // Test database connection
        await testConnection();

        // Initialize database translations (needed for foreign key constraints)
        await DatabaseMigrationService.initializeTranslations();

        // Check foreign key structure and warn if issues exist
        await DatabaseMigrationService.checkForeignKeyStructure();

        // Start server
        const server = app.listen(PORT,() => {
             console.log(`Server is running on port ${PORT}`);
        });

        // Initialize Socket.IO and store the instance
        socketManager = new SocketManager(server);
        console.log('Socket.IO server initialized');

        // Initialize the game end scheduler
        gameEndScheduler = GameEndScheduler.init({
            schedule: process.env.GAME_END_SCHEDULE || '0 * * * *' // Default: Run every hour
        });
        console.log('Game end scheduler initialized');

        // Start the periodic game end checks in socket manager
        socketManager.startPeriodicEndGame();
        console.log('Socket game end periodic checks started (running every second)');

        // Handle unhandled promise rejections
        process.on('unhandledRejection', (err) => {
            console.error('UNHANDLED REJECTION! 💥 Shutting down...');
            console.error(err.name, err.message);
            gracefulShutdown(server, 1);
        });

        // Return the server instance for testing purposes
        return server;
    } catch (error) {
        console.error('Error starting server:', error.message);
        process.exit(1);
    }
};

// Start the server
if (require.main === module) {
    startServer();
}

// Export for testing
module.exports = { startServer };
