const { pool } = require('../config/config');
const ChallengeUserMappingModel = require('../models/challengeUserMappingModel');
const leaderBoardModel = require('../models/leaderboardModel');
const RecentActivity = require('../models/recent_activityModel');

/**
 * Service to handle automatic game ending
 */
class GameEndService {
  // Cache to store the last check time for each challenge
  static lastCheckTimes = new Map();

  /**
   * Checks for challenges that have reached their end_date and ends them
   * This method should be called by a scheduler/cron job
   * Optimized for frequent calls (e.g., every second)
   */
  static async checkAndEndExpiredChallenges() {
    try {
      const now = new Date();

      // Only log every 60 seconds to avoid excessive logging
      if (now.getSeconds() === 0) {
        // console.log('GameEndService: Checking for expired challenges...');
      }

      // Find challenges that have reached their end_date but are still active (status = 1)
      const [expiredChallenges] = await pool.query(`
        SELECT id, end_date
        FROM tbl_challenges
        WHERE status = 1
        AND end_date <= NOW()
      `);

      if (!expiredChallenges || expiredChallenges.length === 0) {
        // Only log every 60 seconds to avoid excessive logging
        if (now.getSeconds() === 0) {
          // console.log('GameEndService: No expired challenges found.');
        }
        return { ended: 0 };
      }

      // Filter out challenges that were checked too recently (within the last 5 seconds)
      const challengesToProcess = expiredChallenges.filter(challenge => {
        const lastCheckTime = this.lastCheckTimes.get(challenge.id);
        const shouldProcess = !lastCheckTime || (now - lastCheckTime) > 5000; // 5 seconds

        if (shouldProcess) {
          this.lastCheckTimes.set(challenge.id, now);
        }

        return shouldProcess;
      });

      if (challengesToProcess.length === 0) {
        return { ended: 0 };
      }

     

      // End each expired challenge
      const endResults = await Promise.all(
        challengesToProcess.map(async (challenge) => {
          try {
            // Update challenge status to ended (2)
            await pool.query(`
              UPDATE tbl_challenges
              SET status = 2, updatedAt = NOW()
              WHERE id = ?
            `, [challenge.id]);

            // Update all user mappings for this challenge to ended (2)
            const [userMappings] = await pool.query(`
              SELECT m.*, u.fullName
              FROM tbl_challenge_user_mapping m
              JOIN tbl_users u ON m.userId = u.id
              WHERE m.challenge_id = ? AND m.status = 1
            `, [challenge.id]);

            // Update status to ended (2)
            await pool.query(`
              UPDATE tbl_challenge_user_mapping
              SET status = 2, updatedAt = NOW()
              WHERE challenge_id = ?
            `, [challenge.id]);

            // Update ranks for all users in this challenge
            await ChallengeUserMappingModel.updateRanks(challenge.id);

            // Check for winners and add them to the winners table
            await this.processWinners(challenge.id);

            // Log recent activity for each user who was in the challenge
            if (userMappings && userMappings.length > 0) {
              for (const user of userMappings) {
                try {
                  await RecentActivity.create({
                    userId: user.userId,
                    activity: `Challenge ended: Challenge ID ${challenge.id}`
                  });
                
                } catch (activityError) {
                  // Don't fail the challenge end if activity logging fails
                  console.error(`GameEndService: Failed to log recent activity for challenge end: ${activityError.message}`);
                }
              }
            }

           
            return { id: challenge.id, success: true };
          } catch (error) {
            console.error(`GameEndService: Error ending challenge ID: ${challenge.id}:`, error);
            return { id: challenge.id, success: false, error: error.message };
          }
        })
      );

      const successfullyEnded = endResults.filter(result => result.success).length;
     

      return {
        ended: successfullyEnded,
        total: expiredChallenges.length,
        results: endResults
      };
    } catch (error) {
      console.error('GameEndService: Error checking for expired challenges:', error);
      throw error;
    }
  }

  /**
   * Process winners for a challenge
   * @param {number} challengeId - The ID of the challenge
   */
  static async processWinners(challengeId) {
    try {
      // console.log(`GameEndService: Processing winners for challenge ID: ${challengeId}`);

      // Get the challenge details including winning_rules
      const [challengeResult] = await pool.query(`
        SELECT * FROM tbl_challenges
        WHERE id = ?
      `, [challengeId]);

      if (!challengeResult || challengeResult.length === 0) {
        throw new Error(`Challenge with ID ${challengeId} not found`);
      }

      const challenge = challengeResult[0];

      // Parse winning_rules if it's a string
      let winningRules = challenge.winning_rules;
      if (typeof winningRules === 'string') {
        try {
          winningRules = JSON.parse(winningRules);
        } catch (error) {
          console.error(`Error parsing winning_rules for challenge ID ${challengeId}:`, error);
          throw new Error(`Invalid winning_rules format for challenge ID ${challengeId}`);
        }
      } else if (!winningRules) {
        // Default winning rules if none are specified
        winningRules = { level: 1, winning_points: 0, rank: 999 };
      }

      // Get all users in this challenge
      const [users] = await pool.query(`
        SELECT * FROM tbl_challenge_user_mapping
        WHERE challenge_id = ?
      `, [challengeId]);

      if (!users || users.length === 0) {
        // console.log(`No users found for challenge ID: ${challengeId}`);
        return [];
      }

      // Check each user against the winning rules
      const winners = [];
      for (const user of users) {
        const isWinner = (
          (!winningRules.level || user.level >= winningRules.level) &&
          (!winningRules.winning_points || user.score >= winningRules.winning_points) &&
          (!winningRules.rank || user.rank <= winningRules.rank)
        );

        if (isWinner) {
          // Add user to winners table if they're not already there
          const [existingWinner] = await pool.query(`
            SELECT * FROM tbl_game_winners
            WHERE challengeId = ? AND userId = ?
          `, [challengeId, user.userId]);

          if (!existingWinner || existingWinner.length === 0) {
            try {
              await pool.query(`
                INSERT INTO tbl_game_winners (id, userId, challengeId, createdAt, updatedAt)
                VALUES (NULL, ?, ?, NOW(), NOW())
              `, [user.userId, challengeId]);
            } catch (insertError) {
              // If the error is about the id field, try with auto-increment
              if (insertError.code === 'ER_NO_DEFAULT_FOR_FIELD' && insertError.sqlMessage.includes("Field 'id' doesn't have a default value")) {
                // console.log(`GameEndService: Trying alternative insert for winner (userId: ${user.userId}, challengeId: ${challengeId})`);
                await pool.query(`
                  INSERT INTO tbl_game_winners (userId, challengeId, createdAt, updatedAt)
                  SELECT ?, ?, NOW(), NOW()
                  FROM DUAL
                  WHERE NOT EXISTS (
                    SELECT 1 FROM tbl_game_winners WHERE userId = ? AND challengeId = ?
                  )
                `, [user.userId, challengeId, user.userId, challengeId]);
              } else {
                throw insertError;
              }
            }

            // console.log(`GameEndService: Added user ID ${user.userId} as winner for challenge ID ${challengeId}`);
          } else {
            // console.log(`GameEndService: User ID ${user.userId} is already a winner for challenge ID ${challengeId}`);
          }

          winners.push({
            userId: user.userId,
            score: user.score,
            level: user.level,
            rank: user.rank
          });
        }
      }

      // console.log(`GameEndService: Processed ${winners.length} winners for challenge ID: ${challengeId}`);
      return winners;
    } catch (error) {
      console.error(`GameEndService: Error processing winners for challenge ID ${challengeId}:`, error);
      throw error;
    }
  }

  /**
   * Manually end a specific challenge
   * @param {number} challengeId - The ID of the challenge to end
   */
  static async endChallenge(challengeId) {
    try {
      // console.log(`GameEndService: Manually ending challenge ID: ${challengeId}`);

      // Check if challenge exists
      const [challengeResult] = await pool.query(`
        SELECT * FROM tbl_challenges
        WHERE id = ?
      `, [challengeId]);

      if (!challengeResult || challengeResult.length === 0) {
        throw new Error(`Challenge with ID ${challengeId} not found`);
      }

      const challenge = challengeResult[0];

      // Check if challenge is already ended
      if (challengeResult[0].status === 2) {
        // console.log(`GameEndService: Challenge ID ${challengeId} is already ended.`);
        return {
          success: true,
          message: 'Challenge is already ended',
          alreadyEnded: true
        };
      }

      // Update challenge status to ended (2)
      await pool.query(`
        UPDATE tbl_challenges
        SET status = 2, updatedAt = NOW()
        WHERE id = ?
      `, [challengeId]);

      // Get all users in this challenge who are still active
      const [userMappings] = await pool.query(`
        SELECT m.*, u.fullName
        FROM tbl_challenge_user_mapping m
        JOIN tbl_users u ON m.userId = u.id
        WHERE m.challenge_id = ? AND m.status = 1
      `, [challengeId]);

      // Update all user mappings for this challenge to ended (2)
      await pool.query(`
        UPDATE tbl_challenge_user_mapping
        SET status = 2, updatedAt = NOW()
        WHERE challenge_id = ?
      `, [challengeId]);

      // Update ranks for all users in this challenge
      const rankResults = await ChallengeUserMappingModel.updateRanks(challengeId);

      // Process winners
      const winners = await this.processWinners(challengeId);

      // Log recent activity for each user who was in the challenge
      if (userMappings && userMappings.length > 0) {
        for (const user of userMappings) {
          try {
            await RecentActivity.create({
              userId: user.userId,
              activity: `Challenge ended: Challenge ID ${challengeId}`
            });
            // console.log(`GameEndService: Logged recent activity for user ${user.userId} completing challenge ${challengeId}`);
          } catch (activityError) {
            // Don't fail the challenge end if activity logging fails
            console.error(`GameEndService: Failed to log recent activity for challenge end: ${activityError.message}`);
          }
        }
      }

      // console.log(`GameEndService: Successfully ended challenge ID: ${challengeId}`);

      return {
        success: true,
        message: 'Challenge ended successfully',
        challengeId,
        rankResults,
        winners
      };
    } catch (error) {
      console.error(`GameEndService: Error ending challenge ID: ${challengeId}:`, error);
      throw error;
    }
  }
}

module.exports = GameEndService;
