const settingsModel = require('../models/settingsModel');
const { AppError } = require('../middleware/errorMiddleware');
// Function to get all settings
const getQuestionCategory = async () => {
  try {
    const settings = await settingsModel.getQuestionCategory();
    return settings;
  } catch (error) {
    throw new AppError('Failed to get settings', 500);
  }
};

const getChallengePeriod = async () => {
  try {
    const settings = await settingsModel.getChallengePeriod();
    return settings;
  } catch (error) {
    throw new AppError('Failed to get settings', 500);
  }
};


module.exports = {
    getQuestionCategory,  
    getChallengePeriod,

};