const CategoriesIcon = require('../models/categoriesIconsModel');
const ChallengeCategoriesIcon = require('../models/challengeCategoriesIconsModel');
const { AppError } = require('../middleware/errorMiddleware');

class IconsService {
    // Categories Icons
    static async getAllCategoriesIcons() {
        try {
            return await CategoriesIcon.findAll();
        } catch (error) {
            throw new AppError('Error fetching categories icons', 500);
        }
    }

    static async getCategoriesIconById(id) {
        try {
            const icon = await CategoriesIcon.findById(id);
            if (!icon) {
                throw new AppError('Categories icon not found', 404);
            }
            return icon;
        } catch (error) {
            if (error instanceof AppError) {
                throw error;
            }
            throw new AppError('Error fetching categories icon', 500);
        }
    }

    static async createCategoriesIcon(iconData) {
        try {
            return await CategoriesIcon.create(iconData);
        } catch (error) {
            throw new AppError('Error creating categories icon', 500);
        }
    }

    static async updateCategoriesIcon(id, iconData) {
        try {
            const existingIcon = await CategoriesIcon.findById(id);
            if (!existingIcon) {
                throw new AppError('Categories icon not found', 404);
            }

            return await CategoriesIcon.update(id, iconData);
        } catch (error) {
            if (error instanceof AppError) {
                throw error;
            }
            throw new AppError('Error updating categories icon', 500);
        }
    }

    static async deleteCategoriesIcon(id) {
        try {
            const icon = await CategoriesIcon.findById(id);
            if (!icon) {
                throw new AppError('Categories icon not found', 404);
            }

            const deleted = await CategoriesIcon.delete(id);
            if (!deleted) {
                throw new AppError('Failed to delete categories icon', 500);
            }

            return { message: 'Categories icon deleted successfully' };
        } catch (error) {
            if (error instanceof AppError) {
                throw error;
            }
            throw new AppError('Error deleting categories icon', 500);
        }
    }

    // Challenge Categories Icons
    static async getAllChallengeCategoriesIcons() {
        try {
            return await ChallengeCategoriesIcon.findAll();
        } catch (error) {
            throw new AppError('Error fetching challenge categories icons', 500);
        }
    }

    static async getChallengeCategoriesIconById(id) {
        try {
            const icon = await ChallengeCategoriesIcon.findById(id);
            if (!icon) {
                throw new AppError('Challenge categories icon not found', 404);
            }
            return icon;
        } catch (error) {
            if (error instanceof AppError) {
                throw error;
            }
            throw new AppError('Error fetching challenge categories icon', 500);
        }
    }

    static async createChallengeCategoriesIcon(iconData) {
        try {
            return await ChallengeCategoriesIcon.create(iconData);
        } catch (error) {
            throw new AppError('Error creating challenge categories icon', 500);
        }
    }

    static async updateChallengeCategoriesIcon(id, iconData) {
        try {
            const existingIcon = await ChallengeCategoriesIcon.findById(id);
            if (!existingIcon) {
                throw new AppError('Challenge categories icon not found', 404);
            }

            return await ChallengeCategoriesIcon.update(id, iconData);
        } catch (error) {
            if (error instanceof AppError) {
                throw error;
            }
            throw new AppError('Error updating challenge categories icon', 500);
        }
    }

    static async deleteChallengeCategoriesIcon(id) {
        try {
            const icon = await ChallengeCategoriesIcon.findById(id);
            if (!icon) {
                throw new AppError('Challenge categories icon not found', 404);
            }

            const deleted = await ChallengeCategoriesIcon.delete(id);
            if (!deleted) {
                throw new AppError('Failed to delete challenge categories icon', 500);
            }

            return { message: 'Challenge categories icon deleted successfully' };
        } catch (error) {
            if (error instanceof AppError) {
                throw error;
            }
            throw new AppError('Error deleting challenge categories icon', 500);
        }
    }
}

module.exports = IconsService;
