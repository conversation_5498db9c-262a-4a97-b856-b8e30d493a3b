const ChallengeCategoryService = require('../services/challengeCategoryService');

class ChallengeCategoryController {
    static async getAllCategories(req, res, next) {
        try {
            const categories = await ChallengeCategoryService.getAllCategories();
            res.success(categories, 'Categories retrieved successfully');
        } catch (error) {
            next(error);
        }
    }

    static async getCategoryById(req, res, next) {
        try {
            const category = await ChallengeCategoryService.getCategoryById(req.params.id);
            res.success(category, 'Category retrieved successfully');
        } catch (error) {
            next(error);
        }
    }

    static async createCategory(req, res, next) {
        try {
            const category = await ChallengeCategoryService.createCategory(req.body);
            res.created(category, 'Category created successfully');
        } catch (error) {
            next(error);
        }
    }

    static async updateCategory(req, res, next) {
        try {
            const category = await ChallengeCategoryService.updateCategory(req.params.id, req.body);
            res.success(category, 'Category updated successfully');
        } catch (error) {
            next(error);
        }
    }

    static async deleteCategory(req, res, next) {
        try {
            await ChallengeCategoryService.deleteCategory(req.params.id);
            res.success(null, 'Category deleted successfully');
        } catch (error) {
            next(error);
        }
    }

    static async getAllCategoriesPublic(req, res, next) {
        try {
            const categories = await ChallengeCategoryService.getAllCategories();
            res.success(categories, 'Categories retrieved successfully');
        } catch (error) {
            next(error);
        }
    }
}

module.exports = ChallengeCategoryController; 