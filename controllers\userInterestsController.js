const UserInterestsService = require('../services/userInterestsService');
const { AppError } = require('../middleware/errorMiddleware');

class UserInterestsController {
    static async getUserInterests(req, res, next) {
        try {
            const { userId } = req.params;
            const interests = await UserInterestsService.getUserInterests(userId);
            res.success(interests, 'User interests retrieved successfully');
        } catch (error) {
            next(error);
        }
    }

    static async getMyInterests(req, res, next) {
        try {
            const userId = req.user.id;
            const interests = await UserInterestsService.getUserInterests(userId);
            res.success(interests, 'Your interests retrieved successfully');
        } catch (error) {
            next(error);
        }
    }

    static async getUserInterestsWithDetails(req, res, next) {
        try {
            const { userId } = req.params;
            const details = await UserInterestsService.getUserInterestsWithDetails(userId);
            
            if (!details) {
                throw new AppError('User interests not found', 404);
            }
            
            res.success(details, 'User interests with details retrieved successfully');
        } catch (error) {
            next(error);
        }
    }

    static async getMyInterestsWithDetails(req, res, next) {
        try {
            const userId = req.user.id;
            const details = await UserInterestsService.getUserInterestsWithDetails(userId);
            
            if (!details) {
                return res.success({ userId, categories: [] }, 'No interests found');
            }
            
            res.success(details, 'Your interests with details retrieved successfully');
        } catch (error) {
            next(error);
        }
    }

    static async updateUserInterests(req, res, next) {
        try {
            const { userId } = req.params;
            const { categories } = req.body;

            if (!categories) {
                throw new AppError('Categories are required', 400);
            }

            const interests = await UserInterestsService.updateUserInterests(userId, categories);
            res.success(interests, 'User interests updated successfully');
        } catch (error) {
            next(error);
        }
    }

    static async updateMyInterests(req, res, next) {
        try {
            const userId = req.user.id;
            const { categories } = req.body;

            if (!categories) {
                throw new AppError('Categories are required', 400);
            }

            const interests = await UserInterestsService.updateUserInterests(userId, categories);
            res.success(interests, 'Your interests updated successfully');
        } catch (error) {
            next(error);
        }
    }

    static async createUserInterests(req, res, next) {
        try {
            const { userId, categories } = req.body;

            if (!userId || !categories) {
                throw new AppError('userId and categories are required', 400);
            }

            const interests = await UserInterestsService.createUserInterests(userId, categories);
            res.success(interests, 'User interests created successfully');
        } catch (error) {
            next(error);
        }
    }

    static async createMyInterests(req, res, next) {
        try {
            const userId = req.user.id;
            const { categories } = req.body;

            if (!categories) {
                throw new AppError('Categories are required', 400);
            }

            const interests = await UserInterestsService.createUserInterests(userId, categories);
            res.success(interests, 'Your interests created successfully');
        } catch (error) {
            next(error);
        }
    }

    static async deleteUserInterests(req, res, next) {
        try {
            const { userId } = req.params;
            const result = await UserInterestsService.deleteUserInterests(userId);
            res.success(result, 'User interests deleted successfully');
        } catch (error) {
            next(error);
        }
    }

    static async deleteMyInterests(req, res, next) {
        try {
            const userId = req.user.id;
            const result = await UserInterestsService.deleteUserInterests(userId);
            res.success(result, 'Your interests deleted successfully');
        } catch (error) {
            next(error);
        }
    }

    static async addCategoryToUserInterests(req, res, next) {
        try {
            const { userId } = req.params;
            const { categoryId } = req.body;

            if (!categoryId) {
                throw new AppError('categoryId is required', 400);
            }

            const interests = await UserInterestsService.addCategoryToUserInterests(userId, categoryId);
            res.success(interests, 'Category added to user interests successfully');
        } catch (error) {
            next(error);
        }
    }

    static async addCategoryToMyInterests(req, res, next) {
        try {
            const userId = req.user.id;
            const { categoryId } = req.body;

            if (!categoryId) {
                throw new AppError('categoryId is required', 400);
            }

            const interests = await UserInterestsService.addCategoryToUserInterests(userId, categoryId);
            res.success(interests, 'Category added to your interests successfully');
        } catch (error) {
            next(error);
        }
    }

    static async removeCategoryFromUserInterests(req, res, next) {
        try {
            const { userId, categoryId } = req.params;
            const interests = await UserInterestsService.removeCategoryFromUserInterests(userId, categoryId);
            res.success(interests, 'Category removed from user interests successfully');
        } catch (error) {
            next(error);
        }
    }

    static async removeCategoryFromMyInterests(req, res, next) {
        try {
            const userId = req.user.id;
            const { categoryId } = req.params;
            const interests = await UserInterestsService.removeCategoryFromUserInterests(userId, categoryId);
            res.success(interests, 'Category removed from your interests successfully');
        } catch (error) {
            next(error);
        }
    }
}

module.exports = UserInterestsController;
