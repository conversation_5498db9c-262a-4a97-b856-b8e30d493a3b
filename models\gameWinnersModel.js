const { pool } = require('../config/config');

class GameWinner {
    constructor(winner) {
        this.id = winner.id;
        this.userId = winner.userId;
        this.challengeId = winner.challengeId;
        this.createdAt = winner.createdAt;
        this.updatedAt = winner.updatedAt;
    }

    static async findAll() {
        const [rows] = await pool.query('SELECT * FROM tbl_game_winners ORDER BY createdAt DESC');
        return rows.map(row => new GameWinner(row));
    }

    static async findById(id) {
        const [rows] = await pool.query('SELECT * FROM tbl_game_winners WHERE id = ?', [id]);
        return rows[0] ? new GameWinner(rows[0]) : null;
    }

    static async findByUserId(userId) {
        const [rows] = await pool.query('SELECT * FROM tbl_game_winners WHERE userId = ? ORDER BY createdAt DESC', [userId]);
        return rows.map(row => new GameWinner(row));
    }

    static async findByChallengeId(challengeId) {
        const [rows] = await pool.query('SELECT * FROM tbl_game_winners WHERE challengeId = ? ORDER BY createdAt DESC', [challengeId]);
        return rows.map(row => new GameWinner(row));
    }

    static async findByUserAndChallenge(userId, challengeId) {
        const [rows] = await pool.query('SELECT * FROM tbl_game_winners WHERE userId = ? AND challengeId = ?', [userId, challengeId]);
        return rows[0] ? new GameWinner(rows[0]) : null;
    }

    static async create(winnerData) {
        const [result] = await pool.query(
            'INSERT INTO tbl_game_winners (userId, challengeId, createdAt, updatedAt) VALUES (?, ?, NOW(), NOW())',
            [winnerData.userId, winnerData.challengeId]
        );
        return this.findById(result.insertId);
    }

    static async delete(id) {
        const [result] = await pool.query('DELETE FROM tbl_game_winners WHERE id = ?', [id]);
        return result.affectedRows > 0;
    }

    static async getWinnersWithDetails(challengeId = null, limit = 10) {
        let query = `
            SELECT 
                gw.*,
                u.fullName,
                u.avatar,
                u.profile_image_path,
                c.name as challenge_name,
                c.reward
            FROM tbl_game_winners gw
            JOIN tbl_users u ON gw.userId = u.id
            JOIN tbl_challenges c ON gw.challengeId = c.id
        `;
        
        let params = [];
        if (challengeId) {
            query += ' WHERE gw.challengeId = ?';
            params.push(challengeId);
        }
        
        query += ' ORDER BY gw.createdAt DESC LIMIT ?';
        params.push(limit);

        const [rows] = await pool.query(query, params);
        return rows;
    }
}

module.exports = GameWinner;
