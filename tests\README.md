# Socket.js Unit Test Plan

This document outlines the unit test plan for the socket.js file, focusing on the functionality related to updating the rank field in the tbl_challenge_user_mapping table when user rank changes in socket connections.

## Test Files

1. **socket.test.js**: Tests the core functionality of the SocketManager class
2. **challengeUserMappingModel.test.js**: Tests the ChallengeUserMappingModel, particularly the updateRanks method
3. **leaderboardModel.test.js**: Tests the leaderboardModel's handling of rank data
4. **socket-rank-integration.test.js**: Tests the integration between socket.js and rank updates

## Test Cases

### 1. SocketManager Tests

- **joinGame handler**
  - Should update ranks and emit initial leaderboard data on join
  - Should include rank in gameState when emitting to client

- **leaderboard handler**
  - Should update ranks and emit leaderboard data on manual refresh

- **startPeriodicLeaderboardUpdates**
  - Should update ranks and emit leaderboard data periodically

- **startPeriodicUpdates**
  - Should include rank in gameState when emitting updates

### 2. ChallengeUserMappingModel Tests

- **updateRanks method**
  - Should update ranks based on score in descending order
  - Should return empty array when no users found for challenge
  - Should handle errors properly

- **updateProgress method**
  - Should update score, level, and call updateRanks
  - Should level up when score crosses 100-point threshold

### 3. LeaderboardModel Tests

- **getChallengeLeaderboardData method**
  - Should return leaderboard data with ranks from database
  - Should default rank to 0 if not provided in database
  - Should handle database errors properly
  - Should use default limit of 10 if invalid limit provided

### 4. Socket Rank Integration Tests

- **Rank updates in socket connections**
  - Should update ranks when user joins a challenge
  - Should update ranks when user score changes
  - Should update ranks during periodic leaderboard updates
  - Should update ranks on manual leaderboard refresh
  - Should include rank in gameState updates

## Running the Tests

To run all tests:
```
npm test
```

To run tests with watch mode:
```
npm run test:watch
```

To run tests with coverage:
```
npm run test:coverage
```

## Dependencies

- Jest: Testing framework
- Socket.io: For socket functionality
- JWT: For authentication
- MySQL2: For database operations
