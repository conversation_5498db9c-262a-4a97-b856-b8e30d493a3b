const cron = require('node-cron');
const GameEndService = require('../services/gameEndService');

/**
 * Scheduler to automatically check for expired challenges and end them
 */
class GameEndScheduler {
  /**
   * Initialize the scheduler
   * @param {Object} options - Scheduler options
   * @param {string} options.schedule - Cron schedule expression (default: every hour)
   */
  static init(options = {}) {
    const schedule = options.schedule || '0 * * * *'; // Default: Run every hour at minute 0
    
    // console.log(`GameEndScheduler: Initializing with schedule "${schedule}"`);
    
    // Schedule the task
    const task = cron.schedule(schedule, async () => {
      // console.log('GameEndScheduler: Running scheduled task to check for expired challenges...');
      
      try {
        const result = await GameEndService.checkAndEndExpiredChallenges();
        // console.log(`GameEndScheduler: Task completed. Ended ${result.ended} expired challenges.`);
      } catch (error) {
        // console.error('GameEndScheduler: Error running scheduled task:', error);
      }
    });
    
    // console.log('GameEndScheduler: Scheduler initialized successfully');
    return task;
  }
}

module.exports = GameEndScheduler;
