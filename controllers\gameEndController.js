const GameEndService = require('../services/gameEndService');
const AppError = require('../utils/appError');

/**
 * Controller for game end functionality
 */
class GameEndController {
  /**
   * Check for expired challenges and end them
   * This endpoint can be called by a scheduler/cron job
   */
  static async checkAndEndExpiredChallenges(req, res, next) {
    try {
      const result = await GameEndService.checkAndEndExpiredChallenges();
      res.success(result, `Successfully ended ${result.ended} expired challenges.`);
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Manually end a specific challenge
   */
  static async endChallenge(req, res, next) {
    try {
      const { challengeId } = req.params;
      
      if (!challengeId) {
        throw new AppError('Challenge ID is required', 400);
      }
      
      const result = await GameEndService.endChallenge(challengeId);
      
      if (result.alreadyEnded) {
        res.success(result, `Challenge ${challengeId} was already ended.`);
      } else {
        res.success(result, `Successfully ended challenge ${challengeId}.`);
      }
    } catch (error) {
      next(error);
    }
  }
}

module.exports = GameEndController;
