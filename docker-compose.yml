version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: achawach_backend
    restart: unless-stopped
    ports:
      - "${PORT:-3000}:3000"
    depends_on:
      - db
    environment:
      - NODE_ENV=${NODE_ENV:-development}
      - PORT=${PORT:-3000}
      - DB_HOST=db
      - DB_USER=${DB_USER:-root}
      - DB_PASSWORD=${DB_PASSWORD:-password}
      - DB_NAME=${DB_NAME:-achawach_db}
      - JWT_SECRET=${JWT_SECRET:-your_jwt_secret_key}
    volumes:
      - ./uploads:/usr/src/app/uploads
    networks:
      - achawach-network

  db:
    image: mysql:8.0
    container_name: achawach_mysql
    restart: unless-stopped
    environment:
      - MYSQL_ROOT_PASSWORD=${DB_PASSWORD:-password}
      - MYSQL_DATABASE=${DB_NAME:-achawach_db}
    ports:
      - "3306:3306"
    volumes:
      - mysql-data:/var/lib/mysql
    networks:
      - achawach-network
    command: --default-authentication-plugin=mysql_native_password

volumes:
  mysql-data:

networks:
  achawach-network:
    driver: bridge
