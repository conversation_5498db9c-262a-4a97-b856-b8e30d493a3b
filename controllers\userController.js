const User = require('../models/userModel');
const authService = require('../services/authService');
const { AppError } = require('../middleware/errorMiddleware');
const multer = require('multer');

class UserController {
   
    static async register(req, res, next) {
        try {
            const { fullName, phoneNumber, password, isProfileImageAvailable, avatar,categories } = req.body;
          
            const profileImageFile = req.file; // Access uploaded file information from req.file
    
            // Validate input (rest remains the same)
            if (!fullName || !phoneNumber || !password) {
                throw new AppError('Please provide all required fields', 400);
            }
    
            // Check if user already exists (rest remains the same)
            const existingUser = await User.findByPhone(phoneNumber);
            if (existingUser) {
                throw new AppError('User already exists with this phone number', 400);
            }
    
            let profileData = {};
            if (isProfileImageAvailable === '1') { // Compare with string '1' as it comes from form-data
                if (!profileImageFile) { // Check if a file was actually uploaded when isProfileImageAvailable is '1'
                    throw new AppError('Profile image is required when isProfileImageAvailable is 1', 400);
                }
                const profile_image_path = profileImageFile.path; // Get the file path from req.file.path
                profileData = { profile_image_path, avatar: null };
            } else {
                if (!avatar) { 
                    throw new AppError('Avatar is required when isProfileImageAvailable is not 1', 400);
                }
                profileData = { avatar, profile_image_path: null };
            }
    
            // Create new user with profile data (rest remains the same)
            const user = await User.create({
                fullName,
                phoneNumber,
                password,
                isProfileImageAvailable,
                ...profileData,
                categories: JSON.parse(categories)
            });
            const token = user.generateToken();
    
            res.success({ token }, 'User registered successfully');
        } catch (error) {
            // Handle multer errors (e.g., file size limit exceeded)
            if (error instanceof multer.MulterError) {
                if (error.code === 'LIMIT_FILE_SIZE') {
                    return next(new AppError('Profile image file size too large (max 5MB)', 413)); // 413 Payload Too Large
                }
                // Handle other multer errors if needed
                return next(new AppError(`File upload error: ${error.message}`, 400));
            }
            next(error);
        }
    }

    static async login(req, res, next) {
        try {
            const { phoneNumber, password } = req.body;

            // Validate input
            if (!phoneNumber || !password) {
                throw new AppError('Please provide phone number and password', 400);
            }

            // Find user
            const user = await User.findByPhone(phoneNumber);
            if (!user) {
                throw new AppError('Invalid credentials', 401);
            }

            // Verify password
            const isValid = await user.verifyPassword(password);
            if (!isValid) {
                throw new AppError('Invalid credentials', 401);
            }

            // Generate token
            const token = user.generateToken();

            res.success({ token }, 'Login successful');
        } catch (error) {
            next(error);
        }
    }

    static async getProfile(req, res, next) {
        try {
            const user = await authService.getProfile(req.user.id);
        
            const profile = {
                id: user.id,
                fullName: user.fullName,
                phoneNumber: user.phoneNumber,
                isProfileImageAvailable: user.isProfileImageAvailable,
                avatar: user.avatar, // Ensure correct spelling in DB schema
                profileImagePath: user.profile_image_path,
                createdAt: user.createdAt,
                updatedAt: user.updatedAt,
                categories: user.categories,
                totalEarned: user.totalEarned
            };
    
            res.success(profile, 'Profile retrieved successfully');
        } catch (error) {
            next(error);
        }
    }

    static async updateProfile(req, res, next) {
        try {
            const { fullName, isProfileImageAvailable, avatar, categories } = req.body;
            const profileImageFile = req.file; // Uploaded file from request
    
            // Validate input
            if (!fullName,!isProfileImageAvailable ) {
                throw new AppError('Full name and isProfileImageAvailable are required', 400);
            }
    
            // Check if the user exists
            const userId = req.user.id; // Assuming user ID is available from authentication middleware
            const existingUser = await User.findById(userId);
            if (!existingUser) {
                throw new AppError('User not found', 404);
            }
    
            let updateData = { fullName, categories };
    
          
            updateData.isProfileImageAvailable = isProfileImageAvailable;
            // Handle profile image or avatar
            if(profileImageFile){
                updateData.profile_image_path = profileImageFile.path;
                updateData.avatar = null; // Clear avatar if
            }else{
                if (avatar) {
                    updateData.avatar = avatar;
                    updateData.profile_image_path = null; // Clear image path if avatar is provided
                }
            }
            
            // Update user in DB
           const updatedUser =  await authService.updateProfile(userId, updateData);
    
            res.success({ user: updatedUser }, 'Profile updated successfully');
        } catch (error) {
            // Handle multer errors
            if (error instanceof multer.MulterError) {
                if (error.code === 'LIMIT_FILE_SIZE') {
                    return next(new AppError('Profile image file size too large (max 5MB)', 413));
                }
                return next(new AppError(`File upload error: ${error.message}`, 400));
            }
            next(error);
        }
    }
    
    
static async changePassword(req, res, next) {
        try {
            const { oldPassword, newPassword } = req.body;
            const userId = req.user.id;

            if (!oldPassword || !newPassword) {
                throw new AppError('Please provide old and new passwords', 400);
            }

            const user = await User.findById(userId);
            if (!user) {
                throw new AppError('User not found', 404);
            }

            const isValid = await user.verifyPassword(oldPassword);
            if (!isValid) {
                throw new AppError('Invalid old password', 401);
            }

            await User.changePassword(userId, newPassword);

            res.success({}, 'Password changed successfully');
        } catch (error) {
            next(error);
        }
    }
}

module.exports = UserController;
