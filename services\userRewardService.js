const { pool } = require("../config/config");
const getUserTotalReward = async (userId) => {
    const query = `
        SELECT 
            gw.userId,
            SUM(c.reward) AS total_reward
        FROM 
            tbl_game_winners gw
        JOIN 
            tbl_challenges c ON gw.challengeId = c.id
        WHERE 
            gw.userId = ?
        GROUP BY 
            gw.userId
    `;
    const [rows] = await pool.execute(query, [userId]);
    return rows[0];
};

module.exports = {
    getUserTotalReward
};