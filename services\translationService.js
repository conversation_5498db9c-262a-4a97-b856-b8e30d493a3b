const ChallengeTranslation = require('../models/challengesTranslationModel');
const QuestionTranslation = require('../models/questionsTranslationModel');
const QuestionChoiceTranslation = require('../models/questionsChoicesTranslationModel');
const QuestionCategoryTranslation = require('../models/questionsCategoriesTranslationModel');
const { AppError } = require('../middleware/errorMiddleware');

class TranslationService {
    // Challenge Translations
    static async getChallengeTranslations(challengeId) {
        try {
            return await ChallengeTranslation.findByChallengeId(challengeId);
        } catch (error) {
            throw new AppError('Error fetching challenge translations', 500);
        }
    }

    static async getChallengeTranslation(challengeId, langId) {
        try {
            return await ChallengeTranslation.findByChallengeAndLanguage(challengeId, langId);
        } catch (error) {
            throw new AppError('Error fetching challenge translation', 500);
        }
    }

    static async createChallengeTranslation(translationData) {
        try {
            return await ChallengeTranslation.create(translationData);
        } catch (error) {
            throw new AppError('Error creating challenge translation', 500);
        }
    }

    static async updateChallengeTranslation(challengeId, langId, translationData) {
        try {
            return await ChallengeTranslation.upsert(challengeId, langId, translationData);
        } catch (error) {
            throw new AppError('Error updating challenge translation', 500);
        }
    }

    static async deleteChallengeTranslation(translationId) {
        try {
            const deleted = await ChallengeTranslation.delete(translationId);
            if (!deleted) {
                throw new AppError('Translation not found', 404);
            }
            return { message: 'Challenge translation deleted successfully' };
        } catch (error) {
            if (error instanceof AppError) {
                throw error;
            }
            throw new AppError('Error deleting challenge translation', 500);
        }
    }

    // Question Translations
    static async getQuestionTranslations(questionId) {
        try {
            return await QuestionTranslation.findByQuestionId(questionId);
        } catch (error) {
            throw new AppError('Error fetching question translations', 500);
        }
    }

    static async getQuestionTranslation(questionId, langId) {
        try {
            return await QuestionTranslation.findByQuestionAndLanguage(questionId, langId);
        } catch (error) {
            throw new AppError('Error fetching question translation', 500);
        }
    }

    static async createQuestionTranslation(translationData) {
        try {
            return await QuestionTranslation.create(translationData);
        } catch (error) {
            throw new AppError('Error creating question translation', 500);
        }
    }

    static async updateQuestionTranslation(questionId, langId, translationData) {
        try {
            return await QuestionTranslation.upsert(questionId, langId, translationData);
        } catch (error) {
            throw new AppError('Error updating question translation', 500);
        }
    }

    // Question Choice Translations
    static async getQuestionChoiceTranslations(choiceId) {
        try {
            return await QuestionChoiceTranslation.findByChoiceId(choiceId);
        } catch (error) {
            throw new AppError('Error fetching choice translations', 500);
        }
    }

    static async getQuestionChoicesWithTranslations(questionId, langId) {
        try {
            return await QuestionChoiceTranslation.getChoicesWithTranslations(questionId, langId);
        } catch (error) {
            throw new AppError('Error fetching choices with translations', 500);
        }
    }

    static async createQuestionChoiceTranslation(translationData) {
        try {
            return await QuestionChoiceTranslation.create(translationData);
        } catch (error) {
            throw new AppError('Error creating choice translation', 500);
        }
    }

    static async updateQuestionChoiceTranslation(choiceId, langId, translationData) {
        try {
            return await QuestionChoiceTranslation.upsert(choiceId, langId, translationData);
        } catch (error) {
            throw new AppError('Error updating choice translation', 500);
        }
    }

    // Question Category Translations
    static async getQuestionCategoryTranslations(langId) {
        try {
            return await QuestionCategoryTranslation.getCategoriesWithTranslations(langId);
        } catch (error) {
            throw new AppError('Error fetching category translations', 500);
        }
    }

    static async createQuestionCategoryTranslation(translationData) {
        try {
            return await QuestionCategoryTranslation.create(translationData);
        } catch (error) {
            throw new AppError('Error creating category translation', 500);
        }
    }

    static async updateQuestionCategoryTranslation(categoryIconId, langId, translationData) {
        try {
            return await QuestionCategoryTranslation.upsert(categoryIconId, langId, translationData);
        } catch (error) {
            throw new AppError('Error updating category translation', 500);
        }
    }

    // Bulk operations
    static async getChallengesWithTranslations(langId, limit) {
        try {
            return await ChallengeTranslation.getChallengesWithTranslations(langId, limit);
        } catch (error) {
            throw new AppError('Error fetching challenges with translations', 500);
        }
    }

    static async getQuestionsWithTranslations(langId, limit) {
        try {
            return await QuestionTranslation.getQuestionsWithTranslations(langId, limit);
        } catch (error) {
            throw new AppError('Error fetching questions with translations', 500);
        }
    }
}

module.exports = TranslationService;
