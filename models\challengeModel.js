const { pool } = require("../config/config");

class Challenge {
  constructor(challenge) {
    this.id = challenge.id;
    this.name = challenge.name;

    // Parse winning_rules from JSON string to object if it's a string
    if (challenge.winning_rules && typeof challenge.winning_rules === 'string') {
      try {
        this.winning_rules = JSON.parse(challenge.winning_rules);
      } catch (error) {
        // If parsing fails, keep the original value
        this.winning_rules = challenge.winning_rules;
      }
    } else {
      this.winning_rules = challenge.winning_rules;
    }

    this.reward = challenge.reward;
    this.reward_rules = challenge.reward_rules;
    this.category_id = challenge.category_id;
    this.period_type = challenge.period_type;
    this.createdAt = challenge.createdAt;
    this.updatedAt = challenge.updatedAt;
    this.user_life = challenge.user_life;
    this.category = challenge.category || null;
    this.description = challenge.description;
    this.status = challenge.status;
    this.start_date = challenge.start_date;
    this.end_date = challenge.end_date;
    this.is_winner = challenge.is_winner;
    // this.score = challenge.score;
    // this.level = challenge.level;
    // this.life = challenge.life;
    // this.rank = challenge.rank;
    this.participation_status = challenge.participation_status;
    this.user_progress = challenge.user_progress;


  }

  static async findAll() {
    const [rows] = await pool.query(`
            SELECT c.*, cc.name as category_name, cc.icon as category_icon
            FROM tbl_challenges c
            LEFT JOIN tbl_challenge_categories cc ON c.category_id = cc.id
            ORDER BY c.createdAt DESC
        `);
    return rows.map(
      (row) =>
        new Challenge({
          ...row,
          category: row.category_name
            ? {
                id: row.category_id,
                name: row.category_name,
                icon: row.category_icon,
              }
            : null,
        })
    );
  }

  static async findById(id) {
    const [rows] = await pool.query(
      `
            SELECT c.*, cc.name as category_name, cc.icon as category_icon
            FROM tbl_challenges c
            LEFT JOIN tbl_challenge_categories cc ON c.category_id = cc.id
            WHERE c.id = ?
        `,
      [id]
    );
    if (!rows[0]) return null;

    return new Challenge({
      ...rows[0],
      category: rows[0].category_name
        ? {
            id: rows[0].category_id,
            name: rows[0].category_name,
            icon: rows[0].category_icon,
          }
        : null,
    });
  }

  static async findByCategoryId(categoryId) {
    const [rows] = await pool.query(
      `
            SELECT c.*, cc.name as category_name, cc.icon as category_icon
            FROM tbl_challenges c
            LEFT JOIN tbl_challenge_categories cc ON c.category_id = cc.id
            WHERE c.category_id = ?
            ORDER BY c.createdAt DESC
        `,
      [categoryId]
    );

    return rows.map(
      (row) =>
        new Challenge({
          ...row,
          category: row.category_name
            ? {
                id: row.category_id,
                name: row.category_name,
                icon: row.category_icon,
              }
            : null,
        })
    );
  }

  static async create(challengeData) {
    // Convert winning_rules to JSON string if it's an object
    let winningRules = challengeData.winning_rules;
    if (winningRules && typeof winningRules === 'object') {
      winningRules = JSON.stringify(winningRules);
    }

    const [result] = await pool.query(
      "INSERT INTO tbl_challenges (name, winning_rules, reward, category_id, createdAt, updatedAt) VALUES (?, ?, ?, ?, NOW(), NOW())",
      [
        challengeData.name,
        winningRules,
        challengeData.reward,
        challengeData.category_id,
      ]
    );
    return this.findById(result.insertId);
  }

  static async update(id, challengeData) {
    // Convert winning_rules to JSON string if it's an object
    let winningRules = challengeData.winning_rules;
    if (winningRules && typeof winningRules === 'object') {
      winningRules = JSON.stringify(winningRules);
    }

    await pool.query(
      "UPDATE tbl_challenges SET name = ?, winning_rules = ?, reward = ?, category_id = ?, updatedAt = NOW() WHERE id = ?",
      [
        challengeData.name,
        winningRules,
        challengeData.reward,
        challengeData.category_id,
        id,
      ]
    );
    return this.findById(id);
  }

  static async delete(id) {
    const [result] = await pool.query(
      "DELETE FROM tbl_challenges WHERE id = ?",
      [id]
    );
    return result.affectedRows > 0;
  }

  static async findTopSpecialChallenges(limit = 10, userId) {
    const [rows] = await pool.query(
      `
            SELECT c.*
            FROM tbl_challenges c
            WHERE c.category_id = 0 AND c.status = 1
            AND NOT EXISTS (
                SELECT 1
                FROM tbl_challenge_user_mapping cum
                WHERE cum.challenge_id = c.id
                AND cum.userId = ?
            )

            ORDER BY c.reward DESC
            LIMIT ?
        `,
      [userId, limit]
    );

    return rows.map((row) => new Challenge(row));
  }

  static async searchChallenges(filters, userId = null) {
    let query = `
            SELECT c.*, cc.name as category_name, cc.icon as category_icon,
                   COUNT(DISTINCT cum.userId) as popularity
        `;

    // Add user participation status if userId is provided
    if (userId) {
      query += `,
               CASE
                   WHEN MAX(ucum.userId) IS NULL THEN 'Not Joined'
                   WHEN MAX(ucum.status) = 2 THEN 'Ended'
                   ELSE 'Joined'
               END as participation_status,
               MAX(ucum.score) as user_score,
               MAX(ucum.level) as user_level,
               MAX(ucum.life) as user_life,
               MAX(ucum.rank) as user_rank
        `;
    }

    query += `
            FROM tbl_challenges c
            LEFT JOIN tbl_challenge_categories cc ON c.category_id = cc.id
            LEFT JOIN tbl_challenge_user_mapping cum ON c.id = cum.challenge_id
        `;

    // Add user-specific join if userId is provided
    if (userId) {
      query += `
                LEFT JOIN tbl_challenge_user_mapping ucum ON c.id = ucum.challenge_id AND ucum.userId = ?
            `;
    }

    // Build WHERE clause
    const whereConditions = [];
    const params = userId ? [userId] : [];

    // 1. General Search
    if (filters.search) {
      whereConditions.push(`(c.name LIKE ? OR c.description LIKE ?)`);
      params.push(`%${filters.search}%`, `%${filters.search}%`);
    }

    // 2. Category & Type
    if (filters.category_id) {
      whereConditions.push(`c.category_id = ?`);
      params.push(filters.category_id);
    }

    if (filters.min_reward) {
      whereConditions.push(`c.reward <= ?`);
      params.push(filters.min_reward);
    }

    // 4. Status & Availability
    if (filters.status) {
      whereConditions.push(`c.status = ?`);
      params.push(filters.status);
    }

    // 5. Date Filters
    if (filters.created_date) {
      whereConditions.push(`DATE(c.createdAt) = ?`);
      params.push(filters.created_date);
    }

    if (filters.start_date) {
      whereConditions.push(`c.start_date >= ?`);
      params.push(filters.start_date);
    }

    if (filters.end_date) {
      whereConditions.push(`c.end_date <= ?`);
      params.push(filters.end_date);
    }

    // 6. User Engagement (if userId is provided)
    if (userId && filters.participation_status) {
      if (filters.participation_status === "Not Joined") {
        whereConditions.push(`ucum.userId IS NULL`);
      } else if (filters.participation_status === "Ended") {
        whereConditions.push(`ucum.userId IS NOT NULL AND ucum.status = 2`);
      } else if (filters.participation_status === "Joined") {
        whereConditions.push(`ucum.userId IS NOT NULL AND ucum.status != 2`);
      }
    }

    // Add WHERE clause if conditions exist
    if (whereConditions.length > 0) {
      query += ` WHERE ${whereConditions.join(" AND ")}`;
    }

    // Group by to handle the COUNT aggregate
    query += ` GROUP BY c.id`;

    // 6. Popularity sorting
    if (filters.sort_by === "popularity") {
      query += ` ORDER BY popularity DESC`;
    } else {
      query += ` ORDER BY c.createdAt DESC`;
    }

    // Pagination
    const limit = filters.limit || 10;
    const page = filters.page || 1;
    const offset = (page - 1) * limit;

    query += ` LIMIT ? OFFSET ?`;
    params.push(parseInt(limit), parseInt(offset));

    // Execute query
    const [rows] = await pool.query(query, params);

    // Get total count for pagination
    let countQuery = `
            SELECT COUNT(DISTINCT c.id) as total
            FROM tbl_challenges c
            LEFT JOIN tbl_challenge_categories cc ON c.category_id = cc.id
            LEFT JOIN tbl_challenge_user_mapping cum ON c.id = cum.challenge_id
        `;

    // Add user-specific join if userId is provided
    if (userId) {
      countQuery += `
                LEFT JOIN tbl_challenge_user_mapping ucum ON c.id = ucum.challenge_id AND ucum.userId = ?
            `;
    }

    // Add WHERE clause if conditions exist
    if (whereConditions.length > 0) {
      countQuery += ` WHERE ${whereConditions.join(" AND ")}`;
    }

    const [countResult] = await pool.query(countQuery, params.slice(0, -2)); // Remove limit and offset
    const total = countResult[0].total;

    return {
      challenges: rows.map(
        (row) =>
          new Challenge({
            ...row,
            category: row.category_name
              ? {
                  id: row.category_id,
                  name: row.category_name,
                  icon: row.category_icon,
                }
              : null,
            user_progress: userId && row.user_score !== null
              ? {
                  score: row.user_score,
                  level: row.user_level,
                  life: row.user_life,
                  rank: row.user_rank,
                  status: row.participation_status === 'Ended' ? 2 : (row.participation_status === 'Joined' ? 1 : 0)
                }
              : null,
          })
      ),
      pagination: {
        total,
        page: parseInt(page),
        limit: parseInt(limit),
        pages: Math.ceil(total / limit),
      },
    };
  }

  static async findStartedChallenges(userId) {
    const [rows] = await pool.query(
      `
            SELECT c.*, cc.name as category_name, cc.icon as category_icon,
                   cum.score, cum.status, cum.level, cum.life
            FROM tbl_challenges c
            LEFT JOIN tbl_challenge_categories cc ON c.category_id = cc.id
            INNER JOIN tbl_challenge_user_mapping cum ON c.id = cum.challenge_id
            WHERE cum.userId = ? AND cum.status = 1
            ORDER BY cum.updatedAt DESC
        `,
      [userId]
    );

    return rows.map(
      (row) =>
        new Challenge({
          ...row,
          category: row.category_name
            ? {
                id: row.category_id,
                name: row.category_name,
                icon: row.category_icon,
              }
            : null,
          user_progress: {
            score: row.score,
            status: row.status,
            level: row.level,
            life: row.life,
          },
        })
    );
  }

  // static async findCompletedChallenges(userId) {
  //   const [rows] = await pool.query(
  //     `
  //           SELECT c.*, cc.name as category_name, cc.icon as category_icon,
  //                  cum.score, cum.status, cum.level, cum.life
  //           FROM tbl_challenges c
  //           LEFT JOIN tbl_challenge_categories cc ON c.category_id = cc.id
  //           INNER JOIN tbl_challenge_user_mapping cum ON c.id = cum.challenge_id
  //           WHERE cum.userId = ? AND cum.status = 2
  //           ORDER BY cum.updatedAt DESC
  //       `,
  //     [userId]
  //   );

  //   return rows.map(
  //     (row) =>
  //       new Challenge({
  //         ...row,
  //         category: row.category_name
  //           ? {
  //               id: row.category_id,
  //               name: row.category_name,
  //               icon: row.category_icon,
  //             }
  //           : null,
  //         user_progress: {
  //           score: row.score,
  //           status: row.status,
  //           level: row.level,
  //           life: row.life,
  //         },
  //       })
  //   );
  // }

  static async findCompletedChallenges(userId) {
    const [rows] = await pool.query(
      `
        SELECT
          c.*,
          cc.name AS category_name,
          cc.icon AS category_icon,
          cum.score, cum.status, cum.level, cum.life,cum.rank,
          gw.id AS winner_id
        FROM tbl_challenges c
        LEFT JOIN tbl_challenge_categories cc ON c.category_id = cc.id
        INNER JOIN tbl_challenge_user_mapping cum ON c.id = cum.challenge_id
        LEFT JOIN tbl_game_winners gw
          ON gw.challengeId = c.id AND gw.userId = cum.userId
        WHERE cum.userId = ? AND cum.status = 2
        ORDER BY cum.updatedAt DESC
      `,
      [userId]
    );

    return rows.map(
      (row) =>
        new Challenge({
          ...row,
          category: row.category_name
            ? {
                id: row.category_id,
                name: row.category_name,
                icon: row.category_icon,
              }
            : null,
          user_progress: {
            score: row.score,
            status: row.status,
            level: row.level,
            life: row.life,
            rank: row.rank,
          },
          is_winner: row.winner_id ? true : false,
        })
    );
  }


  static async getChallengeById(challengeId){
    const [rows] = await pool.query(`SELECT * FROM tbl_challenges WHERE id = ?`, [challengeId]);
    return rows[0];
  }

  static async findByStatus(status) {
    const [rows] = await pool.query(
      `
            SELECT c.*, cc.name as category_name, cc.icon as category_icon
            FROM tbl_challenges c
            LEFT JOIN tbl_challenge_categories cc ON c.category_id = cc.id
            WHERE c.status = ?
            ORDER BY c.createdAt DESC
        `,
      [status]
    );

    return rows.map(
      (row) =>
        new Challenge({
          ...row,
          category: row.category_name
            ? {
                id: row.category_id,
                name: row.category_name,
                icon: row.category_icon,
              }
            : null,
        })
    );
  }
}

module.exports = Challenge;
