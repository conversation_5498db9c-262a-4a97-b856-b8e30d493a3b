const express = require('express');
const cors = require('cors');
const morgan = require('morgan');
const { errorHandler } = require('./middleware/errorMiddleware');
const successResponse = require('./middleware/responcesMiddleware');
const routes = require('./routes/routes');

// Initialize express app
const app = express();



// Middleware
app.use(cors()); // Enable CORS for all routes
app.use(morgan('dev')); // HTTP request logger
app.use(express.json()); // Parse JSON bodies
app.use(express.urlencoded({ extended: true })); // Parse URL-encoded bodies
app.use('/uploads', express.static('uploads'));
// Custom response handler middleware
app.use(successResponse);

// Mount API Routes
app.use('/api/v1', routes);

// 404 handler
app.all('*', (req, res, next) => {
    res.status(404).json({
        status: 'error',
        message: `Can't find ${req.originalUrl} on this server!`
    });
});

// Global error handler
app.use(errorHandler);

module.exports = app;
