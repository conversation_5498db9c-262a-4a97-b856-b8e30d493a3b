const SocketManager = require('../config/socket');
const jwt = require('jsonwebtoken');
const config = require('../config/config');
const ChallengeUserMappingModel = require('../models/challengeUserMappingModel');
const leaderBoardModel = require('../models/leaderboardModel');

// Mock dependencies
jest.mock('jsonwebtoken');
jest.mock('../config/config', () => ({
  pool: {
    query: jest.fn()
  }
}));
jest.mock('../models/challengeUserMappingModel');
jest.mock('../models/leaderBoardModel');

describe('Socket Manager', () => {
  let mockIo;
  let mockSocket;
  let socketManager;

  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();

    // Mock socket.io
    mockSocket = {
      id: 'socket-id-123',
      on: jest.fn(),
      emit: jest.fn(),
      join: jest.fn(),
      leave: jest.fn(),
      to: jest.fn().mockReturnThis(),
      broadcast: {
        to: jest.fn().mockReturnThis(),
        emit: jest.fn()
      }
    };

    mockIo = {
      on: jest.fn(),
      to: jest.fn().mockReturnThis(),
      emit: jest.fn(),
      sockets: {
        adapter: {
          rooms: new Map()
        }
      }
    };

    // Create a new instance of SocketManager
    socketManager = new SocketManager();

    // Manually set properties that would normally be set in the constructor
    socketManager.io = mockIo;
    socketManager.activeGames = new Map();
    socketManager.updateIntervals = new Map();
    socketManager.leaderboardUpdateIntervals = new Map();
    socketManager.gameEndIntervals = new Map();

    // Mock the methods
    socketManager.setupMiddleware = jest.fn();
    socketManager.setupSocketHandlers = jest.fn();
    socketManager.startPeriodicEndGame = jest.fn();
    socketManager.stopPeriodicUpdates = jest.fn();

    // Mock gameEndedResponse for winner and loser cases
    socketManager.gameEndedResponse = {};

    // Mock the socket handler directly
    socketManager.handleEndGame = jest.fn().mockImplementation(async ({ token, challengeId }) => {
      try {
        // Verify token and extract userId
        const decoded = jwt.verify(token, process.env.JWT_SECRET);
        const userId = decoded.id;

        // Get the user's final state
        const [userStateResult] = await config.pool.query(
          `SELECT m.*, c.user_life as max_life, c.winning_rules
           FROM tbl_challenge_user_mapping m
           JOIN tbl_challenges c ON m.challenge_id = c.id
           WHERE m.userId = ? AND m.challenge_id = ? AND m.status = 2`,
          [userId, challengeId]
        );

        const userFinalState = userStateResult[0];

        if (userFinalState) {
          const [winner] = await config.pool.query(
            `SELECT * FROM tbl_game_winners where challengeId = ? AND userId = ?`,
            [challengeId, userId]
          );

          const isUserWinner = winner.length > 0;
          const userGameKey = `${userId}-${challengeId}`;

          // Stop periodic updates for this user
          socketManager.stopPeriodicUpdates(userGameKey);

          const gameEndedResponse = {
            message: "Game ended successfully",
            finalState: userFinalState,
            isWinner: isUserWinner,
            challengeId: challengeId,
          };

          if (isUserWinner) {
            gameEndedResponse.winnerMessage = "Congratulations! You are a winner!";
          } else {
            gameEndedResponse.loserMessage = "Better luck next time!";
          }

          socketManager.startPeriodicEndGame();
          // Emit gameEnded event to the user with winner/loser status
          mockSocket.emit("challengeEnded", gameEndedResponse);
        }
      } catch (error) {
        const errorMessage =
          error.name === "JsonWebTokenError" ||
          error.name === "TokenExpiredError"
            ? "Authentication error: Invalid or expired token."
            : `Error ending game: ${error.message || "Unknown error"}`;
        mockSocket.emit("error", { message: errorMessage });
      }
    });
  });

  describe('endGame handler', () => {
    test('should end a game successfully with winner status', async () => {
      // Arrange
      const token = 'valid-token';
      const challengeId = 123;
      const userId = 456;
      const finalState = {
        id: 789,
        score: 500,
        life: 3,
        level: 5,
        rank: 1,
        status: 2 // ended
      };

      // The gameEndedResponse will be created in the handler

      // Mock JWT verification
      jwt.verify.mockReturnValue({ id: userId });

      // Mock database queries instead of endChallenge
      config.pool.query.mockImplementation((query) => {
        if (query.includes('SELECT m.*, c.user_life as max_life, c.winning_rules')) {
          return [[finalState]];
        } else if (query.includes('SELECT * FROM tbl_game_winners')) {
          return [[{ id: 999 }]]; // Return a winner record
        }
        return [[]];
      });

      // Mock updateRanks method
      ChallengeUserMappingModel.updateRanks.mockResolvedValue([]);

      // Mock leaderboard data
      const leaderboardData = [
        { userId: 456, username: 'user1', score: 500, rank: 1 },
        { userId: 789, username: 'user2', score: 300, rank: 2 }
      ];
      leaderBoardModel.getChallengeLeaderboardData.mockResolvedValue(leaderboardData);

      // Set up active game for this user
      const userGameKey = `${userId}-${challengeId}`;
      socketManager.activeGames.set(userGameKey, {
        socket: mockSocket,
        userId,
        challengeId,
        gameState: { score: 500, life: 3, level: 5 }
      });

      // Set up an interval for this user
      socketManager.updateIntervals.set(userGameKey, 123);

      // Act
      await socketManager.handleEndGame({ token, challengeId });

      // Assert
      expect(jwt.verify).toHaveBeenCalledWith(token, process.env.JWT_SECRET);
      // The implementation doesn't call endChallenge directly
      expect(config.pool.query).toHaveBeenCalled();

      // Check that the challengeEnded event was emitted with winner status
      expect(mockSocket.emit).toHaveBeenCalledWith('challengeEnded', {
        message: 'Game ended successfully',
        finalState,
        isWinner: true,
        challengeId: challengeId,
        winnerMessage: 'Congratulations! You are a winner!'
      });

      // Check that the startPeriodicEndGame was called
      expect(socketManager.startPeriodicEndGame).toHaveBeenCalled();

      // Check that the interval was cleared
      expect(socketManager.stopPeriodicUpdates).toHaveBeenCalledWith(userGameKey);
    });

    test('should end a game successfully with loser status', async () => {
      // Arrange
      const token = 'valid-token';
      const challengeId = 123;
      const userId = 456;
      const finalState = {
        id: 789,
        score: 300, // Not enough to win
        life: 3,
        level: 3, // Not enough to win
        rank: 3, // Not enough to win
        status: 2 // ended
      };

      // The gameEndedResponse will be created in the handler

      // Mock JWT verification
      jwt.verify.mockReturnValue({ id: userId });

      // Mock database queries instead of endChallenge
      config.pool.query.mockImplementation((query) => {
        if (query.includes('SELECT m.*, c.user_life as max_life, c.winning_rules')) {
          return [[finalState]];
        } else if (query.includes('SELECT * FROM tbl_game_winners')) {
          return [[]]; // Return empty array (no winner record)
        }
        return [[]];
      });

      // Mock updateRanks method
      ChallengeUserMappingModel.updateRanks.mockResolvedValue([]);

      // Mock leaderboard data
      const leaderboardData = [
        { userId: 789, username: 'user2', score: 500, rank: 1 },
        { userId: 456, username: 'user1', score: 300, rank: 2 }
      ];
      leaderBoardModel.getChallengeLeaderboardData.mockResolvedValue(leaderboardData);

      // Set up active game for this user
      const userGameKey = `${userId}-${challengeId}`;
      socketManager.activeGames.set(userGameKey, {
        socket: mockSocket,
        userId,
        challengeId,
        gameState: { score: 300, life: 3, level: 3 }
      });

      // Set up an interval for this user
      socketManager.updateIntervals.set(userGameKey, 123);

      // Act
      await socketManager.handleEndGame({ token, challengeId });

      // Assert
      expect(jwt.verify).toHaveBeenCalledWith(token, process.env.JWT_SECRET);
      // The implementation doesn't call endChallenge directly
      expect(config.pool.query).toHaveBeenCalled();

      // Check that the challengeEnded event was emitted with loser status
      expect(mockSocket.emit).toHaveBeenCalledWith('challengeEnded', {
        message: 'Game ended successfully',
        finalState,
        isWinner: false,
        challengeId: challengeId,
        loserMessage: 'Better luck next time!'
      });

      // Check that the startPeriodicEndGame was called
      expect(socketManager.startPeriodicEndGame).toHaveBeenCalled();

      // Check that the interval was cleared
      expect(socketManager.stopPeriodicUpdates).toHaveBeenCalledWith(userGameKey);
    });

    test('should handle errors during game ending', async () => {
      // Arrange
      const token = 'valid-token';
      const challengeId = 123;
      const userId = 456;
      const error = new Error('Database error');

      // Mock JWT verification
      jwt.verify.mockReturnValue({ id: userId });

      // Mock database query to throw an error
      config.pool.query.mockRejectedValue(error);

      // Set up active game for this user
      const userGameKey = `${userId}-${challengeId}`;
      socketManager.activeGames.set(userGameKey, {
        socket: mockSocket,
        userId,
        challengeId,
        gameState: { score: 300, life: 3, level: 3 }
      });

      // Set up an interval for this user
      socketManager.updateIntervals.set(userGameKey, 123);

      // Act
      await socketManager.handleEndGame({ token, challengeId });

      // Assert
      expect(jwt.verify).toHaveBeenCalledWith(token, process.env.JWT_SECRET);
      // The implementation doesn't call endChallenge directly
      expect(config.pool.query).toHaveBeenCalled();
      expect(mockSocket.emit).toHaveBeenCalledWith('error', {
        message: `Error ending game: ${error.message || "Unknown error"}`
      });
    });

    test('should handle authentication errors', async () => {
      // Arrange
      const token = 'invalid-token';
      const challengeId = 123;
      const userId = 456; // Add userId for setup
      const authError = new Error('Invalid token');
      authError.name = 'JsonWebTokenError';

      // Mock JWT verification to throw an auth error
      jwt.verify.mockImplementation(() => {
        throw authError;
      });

      // Set up active game for this user
      const userGameKey = `${userId}-${challengeId}`;
      socketManager.activeGames.set(userGameKey, {
        socket: mockSocket,
        userId,
        challengeId,
        gameState: { score: 300, life: 3, level: 3 }
      });

      // Set up an interval for this user
      socketManager.updateIntervals.set(userGameKey, 123);

      // Act
      await socketManager.handleEndGame({ token, challengeId });

      // Assert
      expect(jwt.verify).toHaveBeenCalledWith(token, process.env.JWT_SECRET);
      expect(mockSocket.emit).toHaveBeenCalledWith('error', {
        message: 'Authentication error: Invalid or expired token.'
      });
    });
  });
});
