#!/bin/bash

# Docker helper script for achawach_backend

# Function to display help
show_help() {
    echo "Usage: ./docker-scripts.sh [OPTION]"
    echo "Helper script for Docker operations"
    echo ""
    echo "Options:"
    echo "  build       Build the Docker image"
    echo "  up          Start the containers"
    echo "  down        Stop the containers"
    echo "  logs        Show logs from the app container"
    echo "  restart     Restart the containers"
    echo "  shell       Open a shell in the app container"
    echo "  mysql       Open MySQL CLI in the database container"
    echo "  help        Display this help message"
}

# Check if Docker is installed
check_docker() {
    if ! command -v docker &> /dev/null; then
        echo "Error: Docker is not installed or not in PATH"
        exit 1
    fi

    if ! command -v docker-compose &> /dev/null; then
        echo "Error: Docker Compose is not installed or not in PATH"
        exit 1
    fi
}

# Build the Docker image
build() {
    echo "Building Docker image..."
    docker-compose build
}

# Start the containers
up() {
    echo "Starting containers..."
    docker-compose up -d
    echo "Containers started. App is running at http://localhost:3000"
}

# Stop the containers
down() {
    echo "Stopping containers..."
    docker-compose down
}

# Show logs
logs() {
    echo "Showing logs from app container..."
    docker-compose logs -f app
}

# Restart containers
restart() {
    echo "Restarting containers..."
    docker-compose restart
}

# Open shell in app container
shell() {
    echo "Opening shell in app container..."
    docker-compose exec app /bin/sh
}

# Open MySQL CLI
mysql_cli() {
    echo "Opening MySQL CLI..."
    docker-compose exec db mysql -u root -p achawach_db
}

# Main script logic
check_docker

case "$1" in
    build)
        build
        ;;
    up)
        up
        ;;
    down)
        down
        ;;
    logs)
        logs
        ;;
    restart)
        restart
        ;;
    shell)
        shell
        ;;
    mysql)
        mysql_cli
        ;;
    help|*)
        show_help
        ;;
esac
