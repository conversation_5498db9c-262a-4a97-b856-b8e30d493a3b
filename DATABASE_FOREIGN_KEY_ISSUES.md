# Database Foreign Key Issues and Solutions

## Critical Issues Identified

The current database structure has foreign key constraints that don't align with the application logic:

### 1. Challenge User Mapping Issues
- **Current FK**: `tbl_challenge_user_mapping.challenge_id` → `tbl_challenges_translation.id`
- **Should be**: `tbl_challenge_user_mapping.challenge_id` → `tbl_challenges.id`
- **Problem**: User mappings should reference the main challenge, not a specific translation

### 2. Asked Questions Issues
- **Current FK**: `tbl_asked_questions.qId` → `tbl_questions_translation.id`
- **Should be**: `tbl_asked_questions.qId` → `tbl_questions.id`
- **Problem**: Asked questions should reference the main question, not a specific translation

### 3. Question Choices Issues
- **Current FK**: `tbl_questions_choices.qId` → `tbl_questions_translation.id`
- **Should be**: `tbl_questions_choices.qId` → `tbl_questions.id`
- **Problem**: Choices should reference the main question, not a specific translation

### 4. Game Winners Issues
- **Current FK**: `tbl_game_winners.challengeId` → `tbl_challenges_translation.id`
- **Should be**: `tbl_game_winners.challengeId` → `tbl_challenges.id`
- **Problem**: Winners should reference the main challenge, not a specific translation

## Recommended Database Fixes

```sql
-- Fix Challenge User Mapping FK
ALTER TABLE tbl_challenge_user_mapping DROP FOREIGN KEY ch_key_f;
ALTER TABLE tbl_challenge_user_mapping ADD CONSTRAINT ch_key_f 
    FOREIGN KEY (challenge_id) REFERENCES tbl_challenges(id);

-- Fix Asked Questions FK
ALTER TABLE tbl_asked_questions DROP FOREIGN KEY q_keys;
ALTER TABLE tbl_asked_questions ADD CONSTRAINT q_keys 
    FOREIGN KEY (qId) REFERENCES tbl_questions(id);

-- Fix Question Choices FK
ALTER TABLE tbl_questions_choices DROP FOREIGN KEY q_key;
ALTER TABLE tbl_questions_choices ADD CONSTRAINT q_key 
    FOREIGN KEY (qId) REFERENCES tbl_questions(id);

-- Fix Game Winners FK
ALTER TABLE tbl_game_winners DROP FOREIGN KEY challenge_key_f;
ALTER TABLE tbl_game_winners ADD CONSTRAINT challenge_key_f 
    FOREIGN KEY (challengeId) REFERENCES tbl_challenges(id);
```

## Application Code Implications

Until the database is fixed, the application needs to handle the current structure carefully:

1. **Challenge Operations**: Need to work with translation IDs when inserting/updating user mappings
2. **Question Operations**: Need to work with translation IDs when tracking asked questions
3. **Winner Operations**: Need to work with translation IDs when recording winners

## Current Status

- **Challenges**: 11 records in tbl_challenges, 0 in tbl_challenges_translation
- **User Mappings**: 0 records (cannot insert due to FK constraint issues)
- **Questions**: Need to check translation status
- **Game Winners**: Cannot insert due to FK constraint issues

## Next Steps

1. Fix the foreign key constraints in the database
2. Migrate existing data if needed
3. Update application code to work with corrected relationships
4. Test all functionality with the new structure
