const userRewardModel = require('../services/userRewardService');

const getUserReward = async (req, res,next) => {
    try {
        const userId = parseInt(req.user.id);

        if (isNaN(userId)) {
            return res.status(400).json({ message: 'Invalid userId' });
        }

        const result = await userRewardModel.getUserTotalReward(userId);


        res.success(result,'success'); 
    } catch (error) {
        next(error);
    }
};

module.exports = {
    getUserReward
};
