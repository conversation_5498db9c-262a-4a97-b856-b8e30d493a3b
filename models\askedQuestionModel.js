const { pool } = require('../config/config');

class AskedQuestion {
    constructor(askedQuestion) {
        this.id = askedQuestion.id;
        this.qId = askedQuestion.qId;
        this.userId = askedQuestion.userId;
        this.status = askedQuestion.status;
        this.createdAt = askedQuestion.createdAt;
        this.updatedAt = askedQuestion.updatedAt;
    }

    static async markQuestionAsAsked(userId, questionId) {
        const query = `
            INSERT INTO tbl_asked_questions 
            (qId, userId, status, createdAt, updatedAt) 
            VALUES (?, ?, 1, NOW(), NOW())
        `;
        await pool.query(query, [questionId, userId]);
    }

    static async getAskedQuestions(userId) {
        const query = `
            SELECT qId 
            FROM tbl_asked_questions 
            WHERE userId = ? AND status = 1
        `;
        const [rows] = await pool.query(query, [userId]);
        return rows.map(row => row.qId);
    }

    static async resetAskedQuestions(userId) {
        const query = `
            UPDATE tbl_asked_questions 
            SET status = 0, updatedAt = NOW()
            WHERE userId = ? AND createdAt < DATE_SUB(NOW(), INTERVAL 1 DAY)
        `;
        await pool.query(query, [userId]);
    }
}

module.exports = AskedQuestion; 