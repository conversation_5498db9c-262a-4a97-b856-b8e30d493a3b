const AdminService = require('../services/adminService');
const { AppError } = require('../middleware/errorMiddleware');

class AdminController {
    static async login(req, res, next) {
        try {
            const { username, password } = req.body;

            if (!username || !password) {
                throw new AppError('Username and password are required', 400);
            }

            const result = await AdminService.login(username, password);
            res.success(result, 'Admin login successful');
        } catch (error) {
            next(error);
        }
    }

    static async getAllAdmins(req, res, next) {
        try {
            const admins = await AdminService.getAllAdmins();
            res.success(admins, 'Admins retrieved successfully');
        } catch (error) {
            next(error);
        }
    }

    static async getAdminById(req, res, next) {
        try {
            const { id } = req.params;
            const admin = await AdminService.getAdminById(id);
            res.success(admin, 'Admin retrieved successfully');
        } catch (error) {
            next(error);
        }
    }

    static async getProfile(req, res, next) {
        try {
            const adminId = req.user.id;
            const admin = await AdminService.getAdminById(adminId);
            res.success(admin, 'Admin profile retrieved successfully');
        } catch (error) {
            next(error);
        }
    }

    static async createAdmin(req, res, next) {
        try {
            const { fullName, username, password } = req.body;

            if (!fullName || !username || !password) {
                throw new AppError('Full name, username, and password are required', 400);
            }

            const admin = await AdminService.createAdmin({ fullName, username, password });
            res.success(admin, 'Admin created successfully');
        } catch (error) {
            next(error);
        }
    }

    static async updateAdmin(req, res, next) {
        try {
            const { id } = req.params;
            const { fullName, username, password } = req.body;

            const updateData = {};
            if (fullName) updateData.fullName = fullName;
            if (username) updateData.username = username;
            if (password) updateData.password = password;

            if (Object.keys(updateData).length === 0) {
                throw new AppError('At least one field must be provided for update', 400);
            }

            const admin = await AdminService.updateAdmin(id, updateData);
            res.success(admin, 'Admin updated successfully');
        } catch (error) {
            next(error);
        }
    }

    static async updateProfile(req, res, next) {
        try {
            const adminId = req.user.id;
            const { fullName, username, password } = req.body;

            const updateData = {};
            if (fullName) updateData.fullName = fullName;
            if (username) updateData.username = username;
            if (password) updateData.password = password;

            if (Object.keys(updateData).length === 0) {
                throw new AppError('At least one field must be provided for update', 400);
            }

            const admin = await AdminService.updateAdmin(adminId, updateData);
            res.success(admin, 'Admin profile updated successfully');
        } catch (error) {
            next(error);
        }
    }

    static async deleteAdmin(req, res, next) {
        try {
            const { id } = req.params;
            
            // Prevent admin from deleting themselves
            if (req.user.id === parseInt(id)) {
                throw new AppError('Cannot delete your own admin account', 400);
            }

            const result = await AdminService.deleteAdmin(id);
            res.success(result, 'Admin deleted successfully');
        } catch (error) {
            next(error);
        }
    }
}

module.exports = AdminController;
