const { pool } = require('../config/config');

class QuestionChoiceTranslation {
    constructor(translation) {
        this.id = translation.id;
        this.choice = translation.choice;
        this.choiceId = translation.choiceId;
        this.langId = translation.langId;
        this.createdAt = translation.createdAt;
        this.updatedAt = translation.updatedAt;
    }

    static async findAll() {
        const [rows] = await pool.query('SELECT * FROM tbl_questions_choices_translation ORDER BY createdAt DESC');
        return rows.map(row => new QuestionChoiceTranslation(row));
    }

    static async findById(id) {
        const [rows] = await pool.query('SELECT * FROM tbl_questions_choices_translation WHERE id = ?', [id]);
        return rows[0] ? new QuestionChoiceTranslation(rows[0]) : null;
    }

    static async findByChoiceId(choiceId) {
        const [rows] = await pool.query('SELECT * FROM tbl_questions_choices_translation WHERE choiceId = ?', [choiceId]);
        return rows.map(row => new QuestionChoiceTranslation(row));
    }

    static async findByChoiceAndLanguage(choiceId, langId) {
        const [rows] = await pool.query('SELECT * FROM tbl_questions_choices_translation WHERE choiceId = ? AND langId = ?', [choiceId, langId]);
        return rows[0] ? new QuestionChoiceTranslation(rows[0]) : null;
    }

    static async findByLanguage(langId) {
        const [rows] = await pool.query('SELECT * FROM tbl_questions_choices_translation WHERE langId = ? ORDER BY createdAt DESC', [langId]);
        return rows.map(row => new QuestionChoiceTranslation(row));
    }

    static async create(translationData) {
        const [result] = await pool.query(
            'INSERT INTO tbl_questions_choices_translation (choice, choiceId, langId, createdAt, updatedAt) VALUES (?, ?, ?, NOW(), NOW())',
            [translationData.choice, translationData.choiceId, translationData.langId]
        );
        return this.findById(result.insertId);
    }

    static async update(id, translationData) {
        await pool.query(
            'UPDATE tbl_questions_choices_translation SET choice = ?, choiceId = ?, langId = ?, updatedAt = NOW() WHERE id = ?',
            [translationData.choice, translationData.choiceId, translationData.langId, id]
        );
        return this.findById(id);
    }

    static async upsert(choiceId, langId, translationData) {
        const existing = await this.findByChoiceAndLanguage(choiceId, langId);
        if (existing) {
            return await this.update(existing.id, { ...translationData, choiceId, langId });
        } else {
            return await this.create({ ...translationData, choiceId, langId });
        }
    }

    static async delete(id) {
        const [result] = await pool.query('DELETE FROM tbl_questions_choices_translation WHERE id = ?', [id]);
        return result.affectedRows > 0;
    }

    static async deleteByChoiceId(choiceId) {
        const [result] = await pool.query('DELETE FROM tbl_questions_choices_translation WHERE choiceId = ?', [choiceId]);
        return result.affectedRows;
    }

    static async getChoicesWithTranslations(questionId, langId = null) {
        let query = `
            SELECT 
                qc.*,
                qct.choice as translated_choice,
                qct.langId
            FROM tbl_questions_choices qc
            LEFT JOIN tbl_questions_choices_translation qct ON qc.id = qct.choiceId
            WHERE qc.qId = ?
        `;
        
        let params = [questionId];
        if (langId) {
            query += ' AND (qct.langId = ? OR qct.langId IS NULL)';
            params.push(langId);
        }
        
        query += ' ORDER BY qc.id ASC';

        const [rows] = await pool.query(query, params);
        return rows;
    }
}

module.exports = QuestionChoiceTranslation;
