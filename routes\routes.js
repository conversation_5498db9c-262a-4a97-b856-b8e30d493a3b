const express = require('express');
const router = express.Router();
const UserController = require('../controllers/userController');
const ChallengeController = require('../controllers/challengeController');
const ChallengeCategoryController = require('../controllers/challengeCategoryController');
const QuestionController = require('../controllers/questionController');
const ChallengeLifeController = require('../controllers/challengeLifeController');
const GameEndController = require('../controllers/gameEndController');
const authenticateToken = require('../middleware/authMiddleware');
const settingsController = require('../controllers/settingsController');
const recent_activityController = require('../controllers/recent_activityContrller');
const multer = require('multer');
const path = require('path'); // Built-in module for path manipulation
const userRewardController = require('../controllers/userRewardController');

// Configure multer storage
const storage = multer.diskStorage({
    destination: function (req, file, cb) {
        cb(null, 'uploads/profile_images/'); // Destination folder for uploaded images
    },
    filename: function (req, file, cb) {
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname)); // Filename: profile_image-timestamp-random.ext
    }
});

// File filter to allow only images (optional, but recommended)
const fileFilter = (req, file, cb) => {
    if (file.mimetype.startsWith('image/')) {
        cb(null, true); // Accept the file
    } else {
        cb(null, false); // Reject the file
        // You can also return an error: cb(new Error('Only image files are allowed!'));
    }
};

const upload = multer({
    storage: storage,
    fileFilter: fileFilter,
    limits: { fileSize: 5 * 1024 * 1024 } // Optional: Limit file size to 5MB
});

// Auth routes
router.post('/auth/register',upload.single('profileImage'), UserController.register);
router.post('/auth/login', UserController.login);
router.post('/auth/change-password', authenticateToken, UserController.changePassword);

// Protected routes
router.get('/profile', authenticateToken, UserController.getProfile);
router.put('/profile',upload.single('profileImage'), authenticateToken, UserController.updateProfile);

// Challenge routes (protected)
router.get('/challenges/search', authenticateToken, ChallengeController.searchChallenges);
router.get('/challenges/special/top', authenticateToken, ChallengeController.getTopSpecialChallenges);
router.get('/challenges/started', authenticateToken, ChallengeController.getStartedChallenges);
router.get('/challenges/completed', authenticateToken, ChallengeController.getCompletedChallenges);
router.get('/challenges/joined', authenticateToken, ChallengeController.getJoinedUserChallenges);
router.get('/challenges', authenticateToken, ChallengeController.getAllChallenges);
router.get('/challenges/:categoryId/started', authenticateToken, ChallengeController.getStartedChallenges);
router.get('/challenges/:categoryId/not-started', authenticateToken, ChallengeController.getNotStartedChallenges);
router.post('/challenges/:id/start', authenticateToken, ChallengeController.startChallenge);
router.get('/challenges/:id', authenticateToken, ChallengeController.getChallengeById);
router.get('/categories/:categoryId/challenges', authenticateToken, ChallengeController.getChallengesByCategory);
router.post('/challenges', authenticateToken, ChallengeController.createChallenge);
router.put('/challenges/:id', authenticateToken, ChallengeController.updateChallenge);
router.delete('/challenges/:id', authenticateToken, ChallengeController.deleteChallenge);


// Challenge life routes
router.get('/challenges/:challengeId/life', authenticateToken, ChallengeLifeController.getCurrentLife);
router.put('/challenges/:challengeId/:qId/life/decrease', authenticateToken, ChallengeLifeController.decreaseLife);
router.put('/challenges/:challengeId/life/increase', authenticateToken, ChallengeLifeController.increaseLife);

// Question routes (protected)
router.get('/challenges/:challengeId/questions', authenticateToken, QuestionController.getQuestions);
router.get('/questions/type/:type', authenticateToken, QuestionController.getQuestionsByType);
router.post('/challenges/:challengeId/answer', authenticateToken, QuestionController.submitAnswer);

// Challenge Category routes (protected)
router.get('/categories', authenticateToken, ChallengeCategoryController.getAllCategories);
router.get('/categories/:id', authenticateToken, ChallengeCategoryController.getCategoryById);
router.post('/categories', authenticateToken, ChallengeCategoryController.createCategory);
router.put('/categories/:id', authenticateToken, ChallengeCategoryController.updateCategory);
router.delete('/categories/:id', authenticateToken, ChallengeCategoryController.deleteCategory);
/**
 * handling life refill
 */
router.post('/check-life', ChallengeLifeController.handleLifeCheckByChallenge);

/**
 * settings routes
 */
router.get('/settings/questions/categories', settingsController.getQuestionCategory);
router.get('/settings/challenges/period', settingsController.getChallengePeriod)

/**
 * Game End routes
 */
router.get('/game-end/check-expired', authenticateToken, GameEndController.checkAndEndExpiredChallenges);
router.post('/game-end/:challengeId', authenticateToken, GameEndController.endChallenge);

/**
 * total won
 */
router.get('/user-earned',authenticateToken, userRewardController.getUserReward);
/**
 * Recent Activity
 */

router.get('/recent-activity',authenticateToken,recent_activityController.getActivityById);
// Health check route
router.get('/health', (req, res) => {
    res.success({ status: 'OK' }, 'API is running');
});

// Public routes
router.get('/public/challenges/top', ChallengeController.getTopChallenges);
router.get('/public/categories', ChallengeCategoryController.getAllCategoriesPublic);
router.get('/public/categories/:categoryId/challenges', ChallengeController.getChallengesByCategoryPublic);

module.exports = router;
