const express = require('express');
const router = express.Router();
const UserController = require('../controllers/userController');
const ChallengeController = require('../controllers/challengeController');
const ChallengeCategoryController = require('../controllers/challengeCategoryController');
const QuestionController = require('../controllers/questionController');
const ChallengeLifeController = require('../controllers/challengeLifeController');
const GameEndController = require('../controllers/gameEndController');
const GameWinnersController = require('../controllers/gameWinnersController');
const AdminController = require('../controllers/adminController');
const UserInterestsController = require('../controllers/userInterestsController');
const TranslationController = require('../controllers/translationController');
const IconsController = require('../controllers/iconsController');
const authenticateToken = require('../middleware/authMiddleware');
const authenticateAdmin = require('../middleware/adminAuthMiddleware');
const settingsController = require('../controllers/settingsController');
const recent_activityController = require('../controllers/recent_activityContrller');
const multer = require('multer');
const path = require('path'); // Built-in module for path manipulation
const userRewardController = require('../controllers/userRewardController');

// Configure multer storage
const storage = multer.diskStorage({
    destination: function (req, file, cb) {
        cb(null, 'uploads/profile_images/'); // Destination folder for uploaded images
    },
    filename: function (req, file, cb) {
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname)); // Filename: profile_image-timestamp-random.ext
    }
});

// File filter to allow only images (optional, but recommended)
const fileFilter = (req, file, cb) => {
    if (file.mimetype.startsWith('image/')) {
        cb(null, true); // Accept the file
    } else {
        cb(null, false); // Reject the file
        // You can also return an error: cb(new Error('Only image files are allowed!'));
    }
};

const upload = multer({
    storage: storage,
    fileFilter: fileFilter,
    limits: { fileSize: 5 * 1024 * 1024 } // Optional: Limit file size to 5MB
});

// Auth routes
router.post('/auth/register',upload.single('profileImage'), UserController.register);
router.post('/auth/login', UserController.login);
router.post('/auth/change-password', authenticateToken, UserController.changePassword);

// Protected routes
router.get('/profile', authenticateToken, UserController.getProfile);
router.put('/profile',upload.single('profileImage'), authenticateToken, UserController.updateProfile);

// Challenge routes (protected)
router.get('/challenges/search', authenticateToken, ChallengeController.searchChallenges);
router.get('/challenges/special/top', authenticateToken, ChallengeController.getTopSpecialChallenges);
router.get('/challenges/started', authenticateToken, ChallengeController.getStartedChallenges);
router.get('/challenges/completed', authenticateToken, ChallengeController.getCompletedChallenges);
router.get('/challenges/joined', authenticateToken, ChallengeController.getJoinedUserChallenges);
router.get('/challenges', authenticateToken, ChallengeController.getAllChallenges);
router.get('/challenges/:categoryId/started', authenticateToken, ChallengeController.getStartedChallenges);
router.get('/challenges/:categoryId/not-started', authenticateToken, ChallengeController.getNotStartedChallenges);
router.post('/challenges/:id/start', authenticateToken, ChallengeController.startChallenge);
router.get('/challenges/:id', authenticateToken, ChallengeController.getChallengeById);
router.get('/categories/:categoryId/challenges', authenticateToken, ChallengeController.getChallengesByCategory);
router.post('/challenges', authenticateToken, ChallengeController.createChallenge);
router.put('/challenges/:id', authenticateToken, ChallengeController.updateChallenge);
router.delete('/challenges/:id', authenticateToken, ChallengeController.deleteChallenge);


// Challenge life routes
router.get('/challenges/:challengeId/life', authenticateToken, ChallengeLifeController.getCurrentLife);
router.put('/challenges/:challengeId/:qId/life/decrease', authenticateToken, ChallengeLifeController.decreaseLife);
router.put('/challenges/:challengeId/life/increase', authenticateToken, ChallengeLifeController.increaseLife);

// Question routes (protected)
router.get('/challenges/:challengeId/questions', authenticateToken, QuestionController.getQuestions);
router.get('/questions/type/:type', authenticateToken, QuestionController.getQuestionsByType);
router.post('/challenges/:challengeId/answer', authenticateToken, QuestionController.submitAnswer);

// Challenge Category routes (protected)
router.get('/categories', authenticateToken, ChallengeCategoryController.getAllCategories);
router.get('/categories/:id', authenticateToken, ChallengeCategoryController.getCategoryById);
router.post('/categories', authenticateToken, ChallengeCategoryController.createCategory);
router.put('/categories/:id', authenticateToken, ChallengeCategoryController.updateCategory);
router.delete('/categories/:id', authenticateToken, ChallengeCategoryController.deleteCategory);
/**
 * handling life refill
 */
router.post('/check-life', ChallengeLifeController.handleLifeCheckByChallenge);

/**
 * settings routes
 */
router.get('/settings/questions/categories', settingsController.getQuestionCategory);
router.get('/settings/challenges/period', settingsController.getChallengePeriod)

/**
 * Game End routes
 */
router.get('/game-end/check-expired', authenticateToken, GameEndController.checkAndEndExpiredChallenges);
router.post('/game-end/:challengeId', authenticateToken, GameEndController.endChallenge);

/**
 * total won
 */
router.get('/user-earned',authenticateToken, userRewardController.getUserReward);
/**
 * Recent Activity
 */

router.get('/recent-activity',authenticateToken,recent_activityController.getActivityById);

/**
 * Game Winners routes
 */
router.get('/game-winners', authenticateToken, GameWinnersController.getAllWinners);
router.get('/game-winners/challenge/:challengeId', authenticateToken, GameWinnersController.getWinnersByChallenge);
router.get('/game-winners/user/:userId', authenticateToken, GameWinnersController.getWinnersByUser);
router.get('/my-wins', authenticateToken, GameWinnersController.getUserWins);
router.post('/game-winners', authenticateToken, GameWinnersController.createWinner);
router.delete('/game-winners/:winnerId', authenticateToken, GameWinnersController.deleteWinner);
router.get('/game-winners/check/:userId/:challengeId', authenticateToken, GameWinnersController.checkWinnerStatus);
router.get('/my-winner-status/:challengeId', authenticateToken, GameWinnersController.checkMyWinnerStatus);

/**
 * Admin routes
 */
router.post('/admin/login', AdminController.login);
router.get('/admin/profile', authenticateAdmin, AdminController.getProfile);
router.put('/admin/profile', authenticateAdmin, AdminController.updateProfile);
router.get('/admin/admins', authenticateAdmin, AdminController.getAllAdmins);
router.get('/admin/admins/:id', authenticateAdmin, AdminController.getAdminById);
router.post('/admin/admins', authenticateAdmin, AdminController.createAdmin);
router.put('/admin/admins/:id', authenticateAdmin, AdminController.updateAdmin);
router.delete('/admin/admins/:id', authenticateAdmin, AdminController.deleteAdmin);

/**
 * User Interests routes
 */
router.get('/user-interests/:userId', authenticateToken, UserInterestsController.getUserInterests);
router.get('/my-interests', authenticateToken, UserInterestsController.getMyInterests);
router.get('/user-interests/:userId/details', authenticateToken, UserInterestsController.getUserInterestsWithDetails);
router.get('/my-interests/details', authenticateToken, UserInterestsController.getMyInterestsWithDetails);
router.put('/user-interests/:userId', authenticateToken, UserInterestsController.updateUserInterests);
router.put('/my-interests', authenticateToken, UserInterestsController.updateMyInterests);
router.post('/user-interests', authenticateToken, UserInterestsController.createUserInterests);
router.post('/my-interests', authenticateToken, UserInterestsController.createMyInterests);
router.delete('/user-interests/:userId', authenticateToken, UserInterestsController.deleteUserInterests);
router.delete('/my-interests', authenticateToken, UserInterestsController.deleteMyInterests);
router.post('/user-interests/:userId/categories', authenticateToken, UserInterestsController.addCategoryToUserInterests);
router.post('/my-interests/categories', authenticateToken, UserInterestsController.addCategoryToMyInterests);
router.delete('/user-interests/:userId/categories/:categoryId', authenticateToken, UserInterestsController.removeCategoryFromUserInterests);
router.delete('/my-interests/categories/:categoryId', authenticateToken, UserInterestsController.removeCategoryFromMyInterests);

/**
 * Translation routes
 */
// Challenge translations
router.get('/translations/challenges/:challengeId', authenticateToken, TranslationController.getChallengeTranslations);
router.get('/translations/challenges/:challengeId/:langId', authenticateToken, TranslationController.getChallengeTranslation);
router.post('/translations/challenges/:challengeId', authenticateToken, TranslationController.createChallengeTranslation);
router.put('/translations/challenges/:challengeId/:langId', authenticateToken, TranslationController.updateChallengeTranslation);
router.delete('/translations/challenges/:translationId', authenticateToken, TranslationController.deleteChallengeTranslation);

// Question translations
router.get('/translations/questions/:questionId', authenticateToken, TranslationController.getQuestionTranslations);
router.get('/translations/questions/:questionId/:langId', authenticateToken, TranslationController.getQuestionTranslation);
router.post('/translations/questions/:questionId', authenticateToken, TranslationController.createQuestionTranslation);
router.put('/translations/questions/:questionId/:langId', authenticateToken, TranslationController.updateQuestionTranslation);

// Question choice translations
router.get('/translations/choices/:choiceId', authenticateToken, TranslationController.getQuestionChoiceTranslations);
router.get('/translations/questions/:questionId/choices', authenticateToken, TranslationController.getQuestionChoicesWithTranslations);
router.post('/translations/choices/:choiceId', authenticateToken, TranslationController.createQuestionChoiceTranslation);
router.put('/translations/choices/:choiceId/:langId', authenticateToken, TranslationController.updateQuestionChoiceTranslation);

// Question category translations
router.get('/translations/question-categories', authenticateToken, TranslationController.getQuestionCategoryTranslations);
router.post('/translations/question-categories', authenticateToken, TranslationController.createQuestionCategoryTranslation);
router.put('/translations/question-categories/:categoryIconId/:langId', authenticateToken, TranslationController.updateQuestionCategoryTranslation);

// Bulk translation operations
router.get('/translations/challenges', authenticateToken, TranslationController.getChallengesWithTranslations);
router.get('/translations/questions', authenticateToken, TranslationController.getQuestionsWithTranslations);

/**
 * Icons routes
 */
// Categories icons
router.get('/icons/categories', authenticateToken, IconsController.getAllCategoriesIcons);
router.get('/icons/categories/:id', authenticateToken, IconsController.getCategoriesIconById);
router.post('/icons/categories', authenticateToken, IconsController.createCategoriesIcon);
router.put('/icons/categories/:id', authenticateToken, IconsController.updateCategoriesIcon);
router.delete('/icons/categories/:id', authenticateToken, IconsController.deleteCategoriesIcon);

// Challenge categories icons
router.get('/icons/challenge-categories', authenticateToken, IconsController.getAllChallengeCategoriesIcons);
router.get('/icons/challenge-categories/:id', authenticateToken, IconsController.getChallengeCategoriesIconById);
router.post('/icons/challenge-categories', authenticateToken, IconsController.createChallengeCategoriesIcon);
router.put('/icons/challenge-categories/:id', authenticateToken, IconsController.updateChallengeCategoriesIcon);
router.delete('/icons/challenge-categories/:id', authenticateToken, IconsController.deleteChallengeCategoriesIcon);

// Health check route
router.get('/health', (req, res) => {
    res.success({ status: 'OK' }, 'API is running');
});

// Public routes
router.get('/public/challenges/top', ChallengeController.getTopChallenges);
router.get('/public/categories', ChallengeCategoryController.getAllCategoriesPublic);
router.get('/public/categories/:categoryId/challenges', ChallengeController.getChallengesByCategoryPublic);

module.exports = router;
