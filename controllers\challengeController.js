const ChallengeService = require('../services/challengeService');

class ChallengeController {
    static async getAllChallenges(req, res, next) {
        try {
            const challenges = await ChallengeService.getAllChallenges();
            res.success(challenges, 'Challenges retrieved successfully');
        } catch (error) {
            next(error);
        }
    }

    static async getChallengeById(req, res, next) {
        try {
            const challenge = await ChallengeService.getChallengeById(req.params.id);
            res.success(challenge, 'Challenge retrieved successfully');
        } catch (error) {
            next(error);
        }
    }

    static async getChallengesByCategory(req, res, next) {
        try {
            const challenges = await ChallengeService.getChallengesByCategory(req.params.categoryId);
            res.success(challenges, 'Challenges for category retrieved successfully');
        } catch (error) {
            next(error);
        }
    }

    static async getStartedChallenges(req, res, next) {
        try {
            const challenges = await ChallengeService.getStartedChallenges(req.user.id, req.params.categoryId);
           
            res.success(challenges, 'Started challenges retrieved successfully');
        } catch (error) {
            next(error);
        }
    }
    static async getJoinedUserChallenges(req, res, next) {
        try {
            const challenges = await ChallengeService.getJoinedChallenges(req.user.id);
           
            res.success(challenges, 'Started challenges retrieved successfully');
        } catch (error) {
            next(error);
        }
    }

    static async getNotStartedChallenges(req, res, next) {
        try {
            
            const challenges = await ChallengeService.getNotStartedChallenges(req.user.id, req.params.categoryId);
            res.success(challenges, 'Not started challenges retrieved successfully');
        } catch (error) {
            next(error);
        }
    }

    static async startChallenge(req, res, next) {
        try {
            const challenge = await ChallengeService.startChallenge(req.user.id, req.params.id);
            res.success(challenge, 'Challenge started successfully');
        } catch (error) {
            next(error);
        }
    }

    static async createChallenge(req, res, next) {
        try {
            const challenge = await ChallengeService.createChallenge(req.body);
            res.created(challenge, 'Challenge created successfully');
        } catch (error) {
            next(error);
        }
    }

    static async updateChallenge(req, res, next) {
        try {
            const challenge = await ChallengeService.updateChallenge(req.params.id, req.body);
            res.success(challenge, 'Challenge updated successfully');
        } catch (error) {
            next(error);
        }
    }

    static async deleteChallenge(req, res, next) {
        try {
            await ChallengeService.deleteChallenge(req.params.id);
            res.success(null, 'Challenge deleted successfully');
        } catch (error) {
            next(error);
        }
    }

    static async getTopSpecialChallenges(req, res, next) {
        try {
            const limit = req.query.limit ? parseInt(req.query.limit) : 10;
            const challenges = await ChallengeService.getTopSpecialChallenges(limit,req.user.id);
            res.success(challenges, 'Top special challenges retrieved successfully');
        } catch (error) {
            next(error);
        }
    }

    static async searchChallenges(req, res, next) {
        try {
            // Extract all query parameters as filters
            const filters = req.query;
            
            // Pass user ID for personalized results
            const userId = req.user.id;
            
            const result = await ChallengeService.searchChallenges(filters, userId);
            
            res.success(result, 'Challenges search results retrieved successfully');
        } catch (error) {
            next(error);
        }
    }

    static async getCompletedChallenges(req, res, next) {
        try {
            const challenges = await ChallengeService.getCompletedChallenges(req.user.id);
            res.success(challenges, 'Completed challenges retrieved successfully');
        } catch (error) {
            next(error);
        }
    }

    static async getTopChallenges(req, res, next) {
        try {
            const challenges = await ChallengeService.getTopChallenges();
            res.success(challenges, 'Top challenges retrieved successfully');
        } catch (error) {
            next(error);
        }
    }

    static async getChallengesByCategoryPublic(req, res, next) {
        try {
            const challenges = await ChallengeService.getChallengesByCategoryId(req.params.categoryId);
            res.success(challenges, 'Challenges for category retrieved successfully');
        } catch (error) {
            next(error);
        }
    }
}

module.exports = ChallengeController; 