const { pool } = require('../config/config');
const ChallengeUserMappingModel = require('../models/challengeUserMappingModel');

// Mock the database pool
jest.mock('../config/config', () => ({
  pool: {
    query: jest.fn()
  }
}));

describe('ChallengeUserMappingModel', () => {
  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks();
  });

  describe('updateRanks', () => {
    test('should update ranks based on score in descending order', async () => {
      // Arrange
      const challengeId = 123;

      // Mock users sorted by score in descending order
      const mockUsers = [
        { id: 2, userId: 102, score: 300 },  // Highest score should get rank 1
        { id: 1, userId: 101, score: 200 },  // Second highest score should get rank 2
        { id: 3, userId: 103, score: 100 }   // Lowest score should get rank 3
      ];

      // Mock the database connection for transaction
      const mockConnection = {
        beginTransaction: jest.fn().mockResolvedValue(undefined),
        query: jest.fn(),
        commit: jest.fn().mockResolvedValue(undefined),
        rollback: jest.fn().mockResolvedValue(undefined),
        release: jest.fn()
      };

      // Mock the first query to get users ordered by score
      pool.query.mockResolvedValueOnce([mockUsers]);

      // Mock getConnection to return our mock connection
      pool.getConnection = jest.fn().mockResolvedValue(mockConnection);

      // Mock the update queries for each user
      mockConnection.query.mockResolvedValueOnce([{ affectedRows: 1 }]); // User with id 2 gets rank 1
      mockConnection.query.mockResolvedValueOnce([{ affectedRows: 1 }]); // User with id 1 gets rank 2
      mockConnection.query.mockResolvedValueOnce([{ affectedRows: 1 }]); // User with id 3 gets rank 3

      // Act
      const result = await ChallengeUserMappingModel.updateRanks(challengeId);

      // Assert
      // First query should get users ordered by score
      expect(pool.query).toHaveBeenNthCalledWith(1, expect.stringContaining('ORDER BY score DESC'), [challengeId]);

      // Should have started a transaction
      expect(mockConnection.beginTransaction).toHaveBeenCalled();

      // Should have made 3 update queries (one for each user)
      expect(mockConnection.query).toHaveBeenCalledTimes(3);

      // Should have committed the transaction
      expect(mockConnection.commit).toHaveBeenCalled();

      // Should have released the connection
      expect(mockConnection.release).toHaveBeenCalled();

      // Check that ranks were assigned correctly based on score
      expect(result).toEqual([
        { id: 2, userId: 102, score: 300, rank: 1 },
        { id: 1, userId: 101, score: 200, rank: 2 },
        { id: 3, userId: 103, score: 100, rank: 3 }
      ]);
    });

    test('should return empty array when no users found for challenge', async () => {
      // Arrange
      const challengeId = 456;

      // Mock the query to return empty array
      pool.query.mockResolvedValueOnce([[]]);

      // Act
      const result = await ChallengeUserMappingModel.updateRanks(challengeId);

      // Assert
      expect(pool.query).toHaveBeenCalledTimes(1);
      expect(pool.query).toHaveBeenCalledWith(expect.any(String), [challengeId]);
      expect(result).toEqual([]);
    });

    test('should handle errors properly', async () => {
      // Arrange
      const challengeId = 789;
      const errorMessage = 'Database error';

      // Mock the query to throw an error
      pool.query.mockRejectedValueOnce(new Error(errorMessage));

      // Act & Assert
      await expect(ChallengeUserMappingModel.updateRanks(challengeId)).rejects.toThrow(errorMessage);
      expect(pool.query).toHaveBeenCalledTimes(1);
    });
  });

  describe('updateProgress', () => {
    test('should update score, level, and call updateRanks', async () => {
      // Arrange
      const userId = 101;
      const challengeId = 123;
      const scoreIncrease = 50;

      // Mock current progress query
      const mockCurrentProgress = [{
        id: 1,
        userId: 101,
        challenge_id: 123,
        score: 100,
        level: 1,
        life: 3,
        status: 1
      }];
      pool.query.mockResolvedValueOnce([mockCurrentProgress]);

      // Mock update progress query
      pool.query.mockResolvedValueOnce([{ affectedRows: 1 }]);

      // Mock updateRanks method
      const originalUpdateRanks = ChallengeUserMappingModel.updateRanks;
      ChallengeUserMappingModel.updateRanks = jest.fn().mockResolvedValue([
        { id: 1, userId: 101, score: 150, rank: 1 }
      ]);

      // Mock query for getting updated rank
      pool.query.mockResolvedValueOnce([[{ rank: 1 }]]);

      // Act
      const result = await ChallengeUserMappingModel.updateProgress(userId, challengeId, scoreIncrease);

      // Assert
      // Should query current progress
      expect(pool.query).toHaveBeenNthCalledWith(
        1,
        expect.stringContaining('SELECT * FROM tbl_challenge_user_mapping'),
        [userId, challengeId]
      );

      // Should update progress with new score and level
      expect(pool.query).toHaveBeenNthCalledWith(
        2,
        expect.stringContaining('UPDATE tbl_challenge_user_mapping'),
        [150, 1, userId, challengeId]
      );

      // Should call updateRanks
      expect(ChallengeUserMappingModel.updateRanks).toHaveBeenCalledWith(challengeId);

      // Should query for updated rank
      expect(pool.query).toHaveBeenNthCalledWith(
        3,
        expect.stringContaining('SELECT rank FROM tbl_challenge_user_mapping'),
        [userId, challengeId]
      );

      // Should return updated progress with rank
      expect(result).toEqual({
        level: 1,
        score: 150,
        life: 3,
        status: 1,
        rank: 1
      });

      // Restore original method
      ChallengeUserMappingModel.updateRanks = originalUpdateRanks;
    });

    test('should level up when score crosses 100-point threshold', async () => {
      // Arrange
      const userId = 101;
      const challengeId = 123;
      const scoreIncrease = 50;

      // Mock current progress query with score at 90
      const mockCurrentProgress = [{
        id: 1,
        userId: 101,
        challenge_id: 123,
        score: 90,
        level: 1,
        life: 3,
        status: 1
      }];
      pool.query.mockResolvedValueOnce([mockCurrentProgress]);

      // Mock update progress query
      pool.query.mockResolvedValueOnce([{ affectedRows: 1 }]);

      // Mock updateRanks method
      const originalUpdateRanks = ChallengeUserMappingModel.updateRanks;
      ChallengeUserMappingModel.updateRanks = jest.fn().mockResolvedValue([
        { id: 1, userId: 101, score: 140, rank: 1 }
      ]);

      // Mock query for getting updated rank
      pool.query.mockResolvedValueOnce([[{ rank: 1 }]]);

      // Act
      const result = await ChallengeUserMappingModel.updateProgress(userId, challengeId, scoreIncrease);

      // Assert
      // Should update progress with new score and increased level (from 1 to 2)
      expect(pool.query).toHaveBeenNthCalledWith(
        2,
        expect.stringContaining('UPDATE tbl_challenge_user_mapping'),
        [140, 2, userId, challengeId] // Level should be 2 now
      );

      // Should return updated progress with increased level
      expect(result).toEqual({
        level: 2, // Level increased
        score: 140,
        life: 3,
        status: 1,
        rank: 1
      });

      // Restore original method
      ChallengeUserMappingModel.updateRanks = originalUpdateRanks;
    });
  });
});
