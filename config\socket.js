const { Server } = require("socket.io");
const jwt = require("jsonwebtoken");
const { pool } = require("./config");
const leaderBoardModel = require("../models/leaderboardModel");
const ChallengeUserMappingModel = require("../models/challengeUserMappingModel");



class SocketManager {
  constructor(server) {
    this.io = new Server(server, {
      cors: {
        origin: "*",
        methods: ["GET", "POST"],
      },
      pingTimeout: 60000, // Keep connection alive settings
      pingInterval: 25000,
      connectTimeout: 45000,
    });

    this.activeGames = new Map();
    this.updateIntervals = new Map(); // For gameState updates
    this.leaderboardUpdateIntervals = new Map(); // For leaderboard updates
    this.gameEndIntervals = new Map(); // For game end checks

    // --- Configuration ---
    this.LEADERBOARD_UPDATE_INTERVAL = 5000; // Update leaderboard every 5 seconds (5000ms)
    this.GAMESTATE_UPDATE_INTERVAL = 200; // Update game state frequently (200ms)
    this.GAME_END_INTERVAL = 1000; // Check for game end every second (1000ms)
    this.DB_OPERATION_TIMEOUT = 5000; // Timeout for database operations (5000ms)

    // console.log("SocketManager: Setting up middleware...");
    this.setupMiddleware();

    // console.log("SocketManager: Setting up handlers...");
    this.setupSocketHandlers();
  }

  /**
   * Cleanup method to clear all timers and resources
   * Call this method when shutting down the server
   */
  cleanup() {
    // console.log("SocketManager: Cleaning up resources...");

    // Clear all gameState update intervals
    for (const [key, intervalId] of this.updateIntervals.entries()) {
      clearInterval(intervalId);
      // console.log(
      //   `SocketManager: Cleared gameState update interval for ${key}`
      // );
    }
    this.updateIntervals.clear();

    // Clear all leaderboard update intervals
    for (const [key, intervalId] of this.leaderboardUpdateIntervals.entries()) {
      clearInterval(intervalId);
      // console.log(
      //   `SocketManager: Cleared leaderboard update interval for ${key}`
      // );
    }
    this.leaderboardUpdateIntervals.clear();

    // Clear all game end intervals
    for (const [key, intervalId] of this.gameEndIntervals.entries()) {
      clearInterval(intervalId);
      // console.log(
      //   `SocketManager: Cleared game end interval for ${key}`
      // );
    }
    this.gameEndIntervals.clear();

    // Clear active games
    this.activeGames.clear();

    // console.log("SocketManager: Cleanup complete");
  }

  setupMiddleware() {
    this.io.use((socket, next) => {
      // console.log(
      //   `SocketManager: Middleware check for incoming connection: ${socket.id}`
      // );
      // Add token validation here if needed globally, or keep it per-event.
      next();
    });
  }

  setupSocketHandlers() {
    this.io.on("connection", (socket) => {
      // console.log(`SocketManager: New client connected, ID: ${socket.id}`);

      socket.onAny((eventName, ...args) => {
        // Avoid logging excessively large data if args contain it
        const logArgs = args.map((arg) =>
          typeof arg === "object" &&
          arg !== null &&
          JSON.stringify(arg).length > 200
            ? "{...data too large...}"
            : arg
        );
        // console.log(
        //   `SocketManager: Received event "${eventName}" from ${socket.id}`,
        //   logArgs
        // );
      });

      // --- joinGame Handler ---
      socket.on("joinGame", async ({ token, challengeId }) => {
        try {
          // console.log(
          //   `SocketManager: joinGame event received for challenge ${challengeId} from ${socket.id}`
          // );
          // Ensure challengeId is a valid number or string format expected by DB
          if (!challengeId) {
            throw new Error("challengeId is required");
          }

          const decoded = jwt.verify(token, process.env.JWT_SECRET);
          const userId = decoded.id;
          // Use challengeId directly in gameKey for consistency if userId isn't needed for the key itself
          // Let's keep userId for mapping though: const gameKey = `${userId}-${challengeId}`;
          const gameKey = `challenge-${challengeId}`; // Room key based only on challenge

          // console.log(
          //   `SocketManager: User ${userId} attempting to join game ${challengeId}. Room key: ${gameKey}`
          // );

          // Fetch initial game state (for this specific user)
          const [gameStateResult] = await pool.query(
            `SELECT cum.*, c.user_life as max_life
             FROM tbl_challenge_user_mapping cum
             JOIN tbl_challenges c ON cum.challenge_id = c.id
             WHERE cum.userId = ? AND cum.challenge_id = ?`,
            [userId, challengeId]
          );

          // Even if game state for *this user* isn't found (e.g., first join),
          // they might still want leaderboard updates. Handle this gracefully.
          let initialGameState = null;
         
          if (gameStateResult.length > 0) {
            initialGameState = gameStateResult[0];
            // console.log(
            //   `SocketManager: Found initial game state for user ${userId}.`
            // );
          } else {
            console.warn(
              `SocketManager: No existing game state found for user ${userId}, challenge ${challengeId}. They can still join the room.`
            );
            // Consider creating an entry if this is the official 'start' mechanism
          }

          // Store/Update active game info - we need a way to track sockets per room/challenge
          // Let's slightly adjust activeGames structure if needed, or assume it's okay.
          // The current `activeGames` key includes userId, which is fine for user-specific state.
          const userGameKey = `${userId}-${challengeId}`; // Key for user-specific data if needed
          this.activeGames.set(userGameKey, {
            socket,
            userId,
            challengeId,
            gameState: initialGameState, // Can be null if user has no state yet
          });
          // console.log(
          //   `SocketManager: Stored user-specific data under key ${userGameKey}`
          // );

          // Join the challenge-specific room
          socket.join(gameKey);
          // console.log(
          //   `SocketManager: Socket ${socket.id} joined room ${gameKey}`
          // );

          // --- Emit Initial States ---
          // Emit initial game state *if available*
          let bonus = 0;
          const [levelDetails] =  await pool.query('select bonus from tbl_level_settings where level=?',[initialGameState.level||1])
          if(levelDetails){
            bonus = levelDetails[0].bonus||0;
          }
          if (initialGameState) {
            const stateToSend = {
              score: initialGameState.score,
              life: initialGameState.life,
              level: initialGameState.level,
              bonus:bonus,
              maxLife: initialGameState.max_life,
              rank: initialGameState.rank || 0, // Include rank in the game state
            };
            socket.emit("gameState", stateToSend);
            // console.log(
            //   `SocketManager: Emitted initial gameState to ${socket.id}:`,
            //   stateToSend
            // );
          }

          // Emit initial leaderboard state immediately on join
          try {
            // First update the ranks in the database and get the results
            const rankResults = await ChallengeUserMappingModel.updateRanks(
              challengeId
            );
            // console.log(
            //   `SocketManager: Updated ranks for challenge ${challengeId} on join`
            // );

            // Then fetch the leaderboard data with updated ranks
            const initialLeaderboard =
              await leaderBoardModel.getChallengeLeaderboardData(
                challengeId,
                10
              );
            socket.emit("gameleaderboardData", {
              leaderboardData: initialLeaderboard,
            });
            // console.log(
            //   `SocketManager: Emitted initial gameleaderboardData to ${socket.id} on join.`
            // );

            // Check if any ranks changed
            const changedRanks = rankResults.filter((r) => r.rankChanged);
            if (changedRanks.length > 0) {
              // console.log(
              //   `SocketManager: ${changedRanks.length} ranks changed for challenge ${challengeId} on user join`
              // );

              // For each user with a changed rank, emit a rankChanged event to their socket
              for (const rankChange of changedRanks) {
                // Find the user's socket
                for (const [key, game] of this.activeGames.entries()) {
                  if (
                    game.userId === rankChange.userId &&
                    game.challengeId === challengeId
                  ) {
                    // Emit rankChanged event to the user
                    game.socket.emit("rankChanged", {
                      challengeId: challengeId,
                      newRank: rankChange.rank,
                      userId: rankChange.userId,
                      onJoin: true, // Flag that this rank change is part of joining
                    });
                    // console.log(
                    //   `SocketManager: Emitted rankChanged event to user ${rankChange.userId} for challenge ${challengeId} on join (socket: ${key})`
                    // );
                    break;
                  }
                }
              }
            }
          } catch (leaderboardError) {
            console.error(
              `SocketManager: Failed to fetch initial leaderboard for ${gameKey} on join:`,
              leaderboardError
            );
            socket.emit("gameleaderboardData", { leaderboardData: [] }); // Send empty on error
          }

          // --- Start Periodic Updates ---
          // Start periodic updates for USER'S game state (if state exists)
          if (initialGameState) {
            this.startPeriodicUpdates(userGameKey); // Use the user-specific key
            // console.log(
            //   `SocketManager: Started gameState periodic updates for user key ${userGameKey}`
            // );
          }

          // <<< Start periodic updates for the CHALLENGE leaderboard (broadcast to room) >>>
          this.startPeriodicLeaderboardUpdates(gameKey, challengeId); // Pass challengeId too
          // console.log(
          //   `SocketManager: Started/Ensured leaderboard periodic updates for room ${gameKey}`
          // );
        } catch (error) {
          console.error(
            `SocketManager: Error in joinGame for challenge ${challengeId} from ${socket.id}:`,
            error
          );
          // Determine if the error is authentication or other
          const errorMessage =
            error.name === "JsonWebTokenError" ||
            error.name === "TokenExpiredError"
              ? "Authentication error: Invalid or expired token."
              : `Error joining game: ${error.message || "Unknown error"}`;
          socket.emit("error", { message: errorMessage });
        }
      });

      // --- leaderboard Handler (Manual Refresh - Optional) ---
      socket.on("leaderboard", async ({ token, challengeId }) => {
        // This can act as an explicit refresh button action from the client
        try {
          // console.log(
          //   `SocketManager: Manual 'leaderboard' refresh requested for challenge ${challengeId} from ${socket.id}`
          // );
          if (!challengeId) throw new Error("challengeId is required");
          // Verify token but we don't need the decoded value
          jwt.verify(token, process.env.JWT_SECRET); // Auth check

          // First update the ranks in the database and get the results
          const rankResults = await ChallengeUserMappingModel.updateRanks(
            challengeId
          );
          // console.log(
          //   `SocketManager: Updated ranks for challenge ${challengeId} on manual refresh`
          // );

          // Then fetch the leaderboard data with updated ranks
          const leaderboardData =
            await leaderBoardModel.getChallengeLeaderboardData(challengeId, 10);
          socket.emit("gameleaderboardData", { leaderboardData }); // Emit back to requester

          // console.log(
          //   `SocketManager: Sent refreshed leaderboard data to ${socket.id}`
          // );

          // Check if any ranks changed
          const changedRanks = rankResults.filter((r) => r.rankChanged);
          if (changedRanks.length > 0) {
            // console.log(
            //   `SocketManager: ${changedRanks.length} ranks changed for challenge ${challengeId} on manual refresh`
            // );

            // For each user with a changed rank, emit a rankChanged event to their socket
            for (const rankChange of changedRanks) {
              // Find the user's socket
              for (const [key, game] of this.activeGames.entries()) {
                if (
                  game.userId === rankChange.userId &&
                  game.challengeId === challengeId
                ) {
                  // Emit rankChanged event to the user
                  game.socket.emit("rankChanged", {
                    challengeId: challengeId,
                    newRank: rankChange.rank,
                    userId: rankChange.userId,
                    manualRefresh: true, // Flag that this rank change is part of manual refresh
                  });
                  // console.log(
                  //   `SocketManager: Emitted rankChanged event to user ${rankChange.userId} for challenge ${challengeId} on manual refresh (socket: ${key})`
                  // );
                  break;
                }
              }
            }
          }
        } catch (error) {
          console.error(
            `SocketManager: Error during manual leaderboard refresh for ${challengeId} from ${socket.id}:`,
            error
          );
          const errorMessage =
            error.name === "JsonWebTokenError" ||
            error.name === "TokenExpiredError"
              ? "Authentication error."
              : `Error getting leaderboard: ${
                  error.message || "Unknown error"
                }`;
          socket.emit("error", { message: errorMessage });
        }
      });

      // --- endGame Handler ---
      socket.on("endGame", async ({ token, challengeId }) => {
        try {
          // console.log(
          //   `SocketManager: endGame event received for challenge ${challengeId} from ${socket.id}`
          // );
          if (!challengeId) throw new Error("challengeId is required");

          // Verify token and extract userId
          const decoded = jwt.verify(token, process.env.JWT_SECRET);
          const userId = decoded.id;

          // console.log(
          //   `SocketManager: User ${userId} attempting to end game ${challengeId}`
          // );

          // Get the user's final state
          const [userStateResult] = await pool.query(
            `
            SELECT m.*, c.user_life as max_life, c.winning_rules,c.reward
            FROM tbl_challenge_user_mapping m
            JOIN tbl_challenges c ON m.challenge_id = c.id
            WHERE m.userId = ? AND m.challenge_id = ? AND m.status = 2
          `,
            [userId, challengeId]
          );



          const userFinalState = userStateResult[0];

          if (userFinalState) {

            const [winner] = await pool.query(
              `
              SELECT * FROM tbl_game_winners where challengeId = ? AND userId = ?
            `,
               [challengeId,userId]
            );
           
            const isUserWinner = winner.length > 0;
            // Get the user-specific game key
            const userGameKey = `${userId}-${challengeId}`;
           

            // Stop periodic updates for this user
            this.stopPeriodicUpdates(userGameKey);


            if (isUserWinner) {
              const gameEndedResponse = {
                message: isUserWinner ?`You Just Won ${userFinalState.reward} ETB`:"Game ended successfully",
                finalState: userFinalState,
                isWinner: isUserWinner,
                challengeId: challengeId,
              };
              gameEndedResponse.winnerMessage =
                "Congratulations! You are a winner!";
              // console.log(
              //   `SocketManager: User ${userId} is a WINNER for challenge ${challengeId}`
              // );
            } else {
              gameEndedResponse.loserMessage = "Better luck next time!";
              // console.log(
              //   `SocketManager: User ${userId} is a LOSER for challenge ${challengeId}`
              // );
            }


            this.startPeriodicEndGame();
            // Emit gameEnded event to the user with winner/loser status
            socket.emit("challengeEnded", gameEndedResponse);

            // console.log(
            //   `SocketManager: Game ended for user ${userId}, challenge ${challengeId}`
            // );



          }
        } catch (error) {
          console.error(
            `SocketManager: Error in endGame for challenge ${challengeId} from ${socket.id}:`,
            error
          );
          const errorMessage =
            error.name === "JsonWebTokenError" ||
            error.name === "TokenExpiredError"
              ? "Authentication error: Invalid or expired token."
              : `Error ending game: ${error.message || "Unknown error"}`;
          socket.emit("error", { message: errorMessage });
        }
      });

      // --- disconnect Handler ---
      socket.on("disconnect", (reason) => {
        // console.log(
        //   `SocketManager: Client ${socket.id} disconnected. Reason: ${reason}`
        // );
        // Clean up resources associated with this socket
        let disconnectedChallengeId = null;
        let roomToLeave = null;

        for (const [userGameKey, game] of this.activeGames.entries()) {
          if (game.socket === socket) {
            // console.log(
            //   `SocketManager: Cleaning up resources for user key ${userGameKey}`
            // );
            this.stopPeriodicUpdates(userGameKey); // Stop user-specific gameState polling
            disconnectedChallengeId = game.challengeId;
            roomToLeave = `challenge-${disconnectedChallengeId}`; // Reconstruct room key
            this.activeGames.delete(userGameKey); // Remove user-specific entry
            // console.log(
            //   `SocketManager: Stopped user gameState updates and removed user key ${userGameKey}`
            // );
            break; // Found the socket's primary entry
          }
        }

        // Check if the room corresponding to the challenge is now empty
        // If so, stop the leaderboard updates for that room
        if (roomToLeave && disconnectedChallengeId) {
          // Get all sockets in the room
          const socketsInRoom = this.io.sockets.adapter.rooms.get(roomToLeave);
          const numSockets = socketsInRoom ? socketsInRoom.size : 0;
          // console.log(
          //   `SocketManager: Sockets remaining in room ${roomToLeave}: ${numSockets}`
          // );

          if (numSockets === 0) {
            // console.log(
            //   `SocketManager: Room ${roomToLeave} is empty. Stopping leaderboard updates.`
            // );
            this.stopPeriodicLeaderboardUpdates(roomToLeave); // Stop leaderboard polling for the room
          } else {
            // console.log(
            //   `SocketManager: Room ${roomToLeave} still has ${numSockets} members. Leaderboard updates continue.`
            // );
          }
        } else {
          // console.log(
          //   `SocketManager: Could not determine room or challengeId for disconnected socket ${socket.id} to check for cleanup.`
          // );
        }
      });

      socket.on("error", (error) => {
        console.error(
          `SocketManager: Socket error event for ${socket.id}:`,
          error
        );
        // Avoid emitting back on generic errors unless needed
      });

     

    });
  }

  /**
   * Start periodic leaderboard updates for a challenge room
   * @param {string} roomKey - The room key (e.g., 'challenge-123')
   * @param {number} challengeId - The challenge ID
   * @returns {number} - The interval ID for testing purposes
   */
  startPeriodicLeaderboardUpdates(roomKey, challengeId) {
    // Check if an interval for this ROOM already exists
    if (this.leaderboardUpdateIntervals.has(roomKey)) {
      // console.log(
      //   `SocketManager: Leaderboard update interval already running for room ${roomKey}.`
      // );
      return this.leaderboardUpdateIntervals.get(roomKey); // Return existing interval ID
    }

    // console.log(
    //   `SocketManager: Setting up new leaderboard update interval for room ${roomKey} (Challenge ID: ${challengeId})`
    // );

    // Keep track of consecutive errors
    let errorCount = 0;
    const MAX_ERRORS = 5; // Stop after 5 consecutive errors

    const intervalId = setInterval(async () => {
      try {
        // Check if the room still exists and has members
        const socketsInRoom = this.io.sockets.adapter.rooms.get(roomKey);
        if (!socketsInRoom || socketsInRoom.size === 0) {
          // console.log(
          //   `SocketManager: Room ${roomKey} is empty or gone. Stopping leaderboard updates.`
          // );
          this.stopPeriodicLeaderboardUpdates(roomKey);
          return;
        }

        // Execute updateRanks with a timeout
        try {
          const updateRanksPromise =
            ChallengeUserMappingModel.updateRanks(challengeId);
          const updateRanksTimeoutPromise = new Promise((_, reject) =>
            setTimeout(
              () => reject(new Error("Update ranks timeout")),
              this.DB_OPERATION_TIMEOUT
            )
          );

          // Race the updateRanks against the timeout
          const rankResults = await Promise.race([
            updateRanksPromise,
            updateRanksTimeoutPromise,
          ]);

          // Check if any ranks changed
          const changedRanks = rankResults.filter((r) => r.rankChanged);
          if (changedRanks.length > 0) {
            // console.log(
            //   `SocketManager: ${changedRanks.length} ranks changed for challenge ${challengeId}`
            // );

            // For each user with a changed rank, emit a rankChanged event to their socket
            for (const rankChange of changedRanks) {
              // Find the user's socket
              for (const [key, game] of this.activeGames.entries()) {
                if (
                  game.userId === rankChange.userId &&
                  game.challengeId === challengeId
                ) {
                  // Emit rankChanged event to the user
                  game.socket.emit("rankChanged", {
                    challengeId: challengeId,
                    newRank: rankChange.rank,
                    userId: rankChange.userId,
                  });
                  // console.log(
                  //   `SocketManager: Emitted rankChanged event to user ${rankChange.userId} for challenge ${challengeId} (socket: ${key})`
                  // );
                  break;
                }
              }
            }
          }
        } catch (updateRanksError) {
          console.error(
            `SocketManager: Error updating ranks: ${updateRanksError.message}`
          );
          // Continue to get leaderboard data even if rank update fails
        }

        // Get leaderboard data with a timeout
        let currentLeaderboard;
        try {
          const leaderboardPromise =
            leaderBoardModel.getChallengeLeaderboardData(challengeId, 10);
          const leaderboardTimeoutPromise = new Promise((_, reject) =>
            setTimeout(
              () => reject(new Error("Leaderboard data timeout")),
              this.DB_OPERATION_TIMEOUT
            )
          );

          // Race the leaderboard query against the timeout
          currentLeaderboard = await Promise.race([
            leaderboardPromise,
            leaderboardTimeoutPromise,
          ]);

          // Ensure we have a valid array
          if (!Array.isArray(currentLeaderboard)) {
            currentLeaderboard = [];
            throw new Error("Invalid leaderboard data format");
          }
        } catch (leaderboardError) {
          console.error(
            `SocketManager: Error getting leaderboard data: ${leaderboardError.message}`
          );
          currentLeaderboard = []; // Use empty array as fallback
        }

        // Reset error count on successful operations
        errorCount = 0;

        // Emit to the ROOM
        this.io
          .to(roomKey)
          .emit("gameleaderboardData", { leaderboardData: currentLeaderboard });
        // console.log(
        //   `SocketManager: Emitted updated leaderboardData to room ${roomKey}`
        // );
      } catch (error) {
        errorCount++;
        console.error(
          `SocketManager: Error in periodic leaderboard update for room ${roomKey} (error ${errorCount}/${MAX_ERRORS}):`,
          error
        );

        // Stop the interval if errors persist
        if (errorCount >= MAX_ERRORS) {
          console.error(
            `SocketManager: Too many errors for room ${roomKey}. Stopping leaderboard updates.`
          );
          this.stopPeriodicLeaderboardUpdates(roomKey);
        }
      }
    }, this.LEADERBOARD_UPDATE_INTERVAL);

    this.leaderboardUpdateIntervals.set(roomKey, intervalId);
    return intervalId; // Return the interval ID for testing purposes
  }

  /**
   * Stop periodic leaderboard updates for a challenge room
   * @param {string} roomKey - The room key (e.g., 'challenge-123')
   * @returns {boolean} - Whether an interval was stopped
   */
  stopPeriodicLeaderboardUpdates(roomKey) {
    const intervalId = this.leaderboardUpdateIntervals.get(roomKey);
    if (intervalId) {
      clearInterval(intervalId);
      this.leaderboardUpdateIntervals.delete(roomKey);
      // console.log(
      //   `SocketManager: Cleared leaderboard update interval for room ${roomKey}`
      // );
      return true;
    }
    return false;
  }

  /**
   * Start periodic updates for a user's game state
   * @param {string} userGameKey - The key identifying the user and challenge (e.g., 'user123-challenge456')
   * @returns {number} - The interval ID for testing purposes
   */
  startPeriodicUpdates(userGameKey) {
    // Stop any existing interval for THIS userGameKey
    this.stopPeriodicUpdates(userGameKey);
    // console.log(
    //   `SocketManager: Setting up new gameState update interval for user key ${userGameKey}`
    // );

    // Keep track of consecutive errors
    let errorCount = 0;
    const MAX_ERRORS = 5; // Stop after 5 consecutive errors

    const intervalId = setInterval(async () => {
      let game = null; // Define game in the outer scope of interval
      try {
        game = this.activeGames.get(userGameKey); // Use user-specific key
        if (!game) {
          // console.warn(
          //   `SocketManager: Game not found for key ${userGameKey}. Stopping interval.`
          // );
          this.stopPeriodicUpdates(userGameKey);
          return;
        }

        // Execute the database query with a timeout
        let currentStateResult;
        try {
          // Create a promise that will reject after timeout
          const dbQueryPromise = pool.query(
            `SELECT cum.*, c.user_life as max_life
             FROM tbl_challenge_user_mapping cum
             JOIN tbl_challenges c ON cum.challenge_id = c.id
             WHERE cum.userId = ? AND cum.challenge_id = ?`,
            [game.userId, game.challengeId]
          );

          
          const timeoutPromise = new Promise((_, reject) =>
            setTimeout(
              () => reject(new Error("Database query timeout")),
              this.DB_OPERATION_TIMEOUT
            )
          );

          // Race the query against the timeout
          const result = await Promise.race([dbQueryPromise, timeoutPromise]);

          // Check if result is an array with at least one element
          if (Array.isArray(result) && result.length > 0) {
            currentStateResult = result[0];
          } else {
            // Handle unexpected result format
            throw new Error("Unexpected database result format");
          }
        } catch (queryError) {
          throw new Error(`Database query failed: ${queryError.message}`);
        }

        // Reset error count on successful query
        errorCount = 0;

        // Check if we got valid results
        if (
          !currentStateResult ||
          !Array.isArray(currentStateResult) ||
          currentStateResult.length === 0
        ) {
          // This specific user's state might have been deleted
          // console.warn(
          //   `SocketManager: User game state query returned no results for active user ${userGameKey}. Stopping interval.`
          // );
          this.stopPeriodicUpdates(userGameKey);
          return;
        }

        // Get the first row of the result
        const dbState = Array.isArray(currentStateResult)
          ? currentStateResult[0]
          : currentStateResult;
        const cachedState = game.gameState; // The user's cached state

        // Check if core game state has changed
        if (
          !cachedState || // If cache was null initially
          dbState.score !== cachedState.score ||
          dbState.life !== cachedState.life ||
          dbState.level !== cachedState.level ||
          dbState.rank !== cachedState.rank // Also check if rank changed
        ) {
          // console.log(
          //   `SocketManager: User game state changed for ${userGameKey}.`
          // );

          // Check if level has changed (level up)
          const hasLeveledUp = cachedState && dbState.level > cachedState.level;

          // Update cached state
          game.gameState = dbState;
          let bonus = 0;
          const [levelDetails] =  await pool.query('select bonus from tbl_level_settings where level=?',[dbState.level||1])
          if(levelDetails){
            bonus = levelDetails[0].bonus||0;
          }
          // Emit updated state ONLY to this user's socket
          const stateToSend = {
            score: dbState.score,
            life: dbState.life,
            level: dbState.level,
            bonus:bonus,
            maxLife: dbState.max_life,
            rank: dbState.rank || 0, // Include rank in the game state
          };

          game.socket.emit("gameState", stateToSend); // Emit directly to the socket
          // console.log(
          //   `SocketManager: Emitted updated gameState to user ${userGameKey}`
          // );

          // If user has leveled up, fetch the level bonus and emit a levelUp event
          if (hasLeveledUp) {
            this.handleLevelUp(game.socket, game.userId, game.challengeId, cachedState.level, dbState.level);
          }

          // Check if game is over (no lives left)
          if (dbState.life <= 0) {
            // console.log(
            //   `SocketManager: Game over detected for user ${userGameKey}. Final Score: ${dbState.score}`
            // );
            game.socket.emit("gameOver", {
              // Emit only to this user
              message: "Game Over! No lives remaining.",
              finalScore: dbState.score,
            });
            this.stopPeriodicUpdates(userGameKey); // Stop updates for this user
          }
        }
      } catch (error) {
        errorCount++;
        console.error(
          `SocketManager: Error in periodic user gameState update for key ${userGameKey} (error ${errorCount}/${MAX_ERRORS}):`,
          error
        );

        // If the game object was retrieved, emit error back to that socket
        if (game && game.socket) {
          game.socket.emit("error", {
            message:
              "Error updating your game state. Please refresh the page if this persists.",
          });
        }

        // Stop the interval if errors persist
        if (errorCount >= MAX_ERRORS) {
          console.error(
            `SocketManager: Too many errors for ${userGameKey}. Stopping updates.`
          );
          this.stopPeriodicUpdates(userGameKey);
        }
      }
    }, this.GAMESTATE_UPDATE_INTERVAL); // Use game state interval time

    this.updateIntervals.set(userGameKey, intervalId);
    return intervalId; // Return the interval ID for testing purposes
  }

  /**
   * Stop periodic updates for a user's game state
   * @param {string} userGameKey - The key identifying the user and challenge
   * @returns {boolean} - Whether an interval was stopped
   */
  stopPeriodicUpdates(userGameKey) {
    const intervalId = this.updateIntervals.get(userGameKey);
    if (intervalId) {
      clearInterval(intervalId);
      this.updateIntervals.delete(userGameKey);
      // console.log(
      //   `SocketManager: Cleared user gameState update interval for ${userGameKey}`
      // );
      return true;
    }
    return false;
  }

  /**
   * Start periodic checks for ended challenges for connected users
   * This checks if any connected users have challenges that have been ended by the scheduler
   * @returns {number} - The interval ID for testing purposes
   */
  startPeriodicEndGame() {
    // Check if an interval already exists
    if (this.gameEndIntervals.has('global')) {
      // console.log(
      //   `SocketManager: Game end check interval already running.`
      // );
      return this.gameEndIntervals.get('global'); // Return existing interval ID
    }

    // console.log(
    //   `SocketManager: Setting up new interval to check for ended challenges for connected users every ${this.GAME_END_INTERVAL}ms`
    // );

    // Keep track of consecutive errors
    let errorCount = 0;
    const MAX_ERRORS = 5; // Stop after 5 consecutive errors

    const intervalId = setInterval(async () => {
      try {
        // Check all active games for ended challenges
        for (const [userGameKey, game] of this.activeGames.entries()) {
          try {
            const userId = game.userId;
            const challengeId = game.challengeId;
            // console.log(`Checking if challenge ${challengeId} has ended for user ${userId}`);

            // Skip if we don't have both userId and challengeId
            if (!userId || !challengeId) continue;

            // Check if this challenge has been ended (status = 2)
            const [result] = await pool.query(
              `SELECT m.*, c.name as challenge_name,c.reward
               FROM tbl_challenge_user_mapping m
               JOIN tbl_challenges c ON m.challenge_id = c.id
               WHERE m.userId = ? AND m.challenge_id = ? AND m.status = 2`,
              [userId, challengeId]
            );

            // If we found a result, the challenge has ended for this user
            if (result && result.length > 0) {
              const userMapping = result[0];
              // Get challenge name for logging purposes
              const [winner] = await pool.query(
                `
                SELECT * FROM tbl_game_winners where challengeId = ? AND userId = ?
              `,
                [challengeId, userId]
              );

              // console.log('---------------',winner,'--------------');
              const isUserWinner = winner.length > 0;
              // console.log('isUserWinner+++++++++++++',isUserWinner);


            // Emit gameEnded event to this specific user
              game.socket.emit('challengeEnded', {
                message: isUserWinner ?`You Just Won ${userMapping.reward} ETB`:"Game ended successfully",
                finalState: userMapping,
                isWinner: isUserWinner,
                challengeId: challengeId,
              });

              // console.log(`SocketManager: Notified user ${userId} about ended challenge ${challengeId}`);
            }
          } catch (userError) {
            console.error(`SocketManager: Error checking ended status for user game ${userGameKey}:`, userError);
            // Continue with other users even if one fails
          }
        }

        // Reset error count on successful operation
        errorCount = 0;
      } catch (error) {
        errorCount++;
        console.error(
          `SocketManager: Error in periodic game end check (error ${errorCount}/${MAX_ERRORS}):`,
          error
        );

        // Stop the interval if errors persist
        if (errorCount >= MAX_ERRORS) {
          console.error(
            `SocketManager: Too many errors in game end checks. Stopping interval.`
          );
          this.stopPeriodicEndGame();
        }
      }
    }, this.GAME_END_INTERVAL);

    this.gameEndIntervals.set('global', intervalId);
    return intervalId; // Return the interval ID for testing purposes
  }

  /**
   * Stop periodic game end checks
   * @returns {boolean} - Whether an interval was stopped
   */
  stopPeriodicEndGame() {
    const intervalId = this.gameEndIntervals.get('global');
    if (intervalId) {
      clearInterval(intervalId);
      this.gameEndIntervals.delete('global');
      // console.log(
      //   `SocketManager: Cleared game end interval`
      // );
      return true;
    }
    return false;
  }

  /**
   * Handle level up event for a user
   * @param {Socket} socket - The user's socket
   * @param {number} userId - The user ID
   * @param {number} challengeId - The challenge ID
   * @param {number} oldLevel - The user's previous level
   * @param {number} newLevel - The user's new level
   */
  async handleLevelUp(socket, userId, challengeId, oldLevel, newLevel) {
    try {
      // Ensure we're only handling one level increment at a time
      if (newLevel !== oldLevel + 1) {
        // console.warn(`SocketManager: Level jump detected from ${oldLevel} to ${newLevel}. Only handling one level at a time.`);
        newLevel = oldLevel + 1;
      }

      // console.log(`SocketManager: User ${userId} leveled up from ${oldLevel} to ${newLevel} in challenge ${challengeId}`);

      // Fetch the level settings to get the bonus
      const [levelSettings] = await pool.query(
        'SELECT * FROM tbl_level_settings WHERE level = ?',
        [newLevel]
      );

      if (levelSettings && levelSettings.length > 0) {
        const levelSetting = levelSettings[0];
        const bonus = Number(levelSetting.bonus);

        // Emit levelUp event to the user
        socket.emit("levelUp", {
          oldLevel,
          newLevel,
          bonus,
          challengeId,
          levelName: levelSetting.name,
          levelStart: Number(levelSetting.start),
          levelEnd: Number(levelSetting.end)
        });

        // console.log(`SocketManager: Emitted levelUp event to user ${userId} with bonus ${bonus}`);
      } else {
        // console.warn(`SocketManager: Could not find level settings for level ${newLevel}`);
      }
    } catch (error) {
      console.error(`SocketManager: Error handling level up for user ${userId}:`, error);
    }
  }
} // End of SocketManager class

module.exports = SocketManager;
