const ChallengeUserMapping = require('../models/challengeUserMappingModel');
const ChallengeLifeService = require('../services/ChallengeLifeService');
const { AppError } = require('../middleware/errorMiddleware');

class ChallengeLifeController {
    static async decreaseLife(req, res, next) {
        try {
            const { challengeId,qId } = req.params;
            
            const result = await ChallengeUserMapping.updateLife(req.user.id, challengeId,qId, true);
            
            if (result.life <= 0) {
                res.success(result, 'Game Over! No lives remaining.');
            } else {
                res.success(result, `Life decreased. ${result.life} lives remaining.`);
            }
        } catch (error) {
            next(error);
        }
    }

    static async increaseLife(req, res, next) {
        try {
            const { challengeId } = req.params;
            
            const result = await ChallengeUserMapping.updateLife(req.user.id, challengeId, false);
            
            res.success(result, `Life increased. Current lives: ${result.life}/${result.maxLife}`);
        } catch (error) {
            next(error);
        }
    }

    static async getCurrentLife(req, res, next) {
        try {
            const { challengeId } = req.params;
            
            const currentProgress = await ChallengeUserMapping.getCurrentProgress(req.user.id, challengeId);
           
            if (!currentProgress) {
                throw new AppError('Challenge not started', 400);
            } 

            

            const progress = {
                life: currentProgress.life,
                maxLife: currentProgress.user_life,
                level: currentProgress.level,
                score: currentProgress.score,
                status: currentProgress.status,
                rank: currentProgress.rank
            }
            
            res.success(progress, 'Challenge life status retrieved successfully');
        } catch (error) {
            next(error);
        }
    }

   static async handleLifeCheckByChallenge(req, res,next) {
        try {
          const { challenge_id } = req.body; // or req.query.challenge_id if you're using query params
    
          if (!challenge_id) {
            throw new AppError('challenge_id is required', 400);
          }
    
          const result = await ChallengeLifeService.checkAndUpdateLifeByChallengeId(challenge_id);
          res.status(200).json(result);
        } catch (err) {
           next(err);
        }
      }
}

module.exports = ChallengeLifeController; 