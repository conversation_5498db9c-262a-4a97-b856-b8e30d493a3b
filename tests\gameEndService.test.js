const GameEndService = require('../services/gameEndService');
const { pool } = require('../config/config');
const ChallengeUserMappingModel = require('../models/challengeUserMappingModel');

// Mock dependencies
jest.mock('../config/config', () => ({
  pool: {
    query: jest.fn(),
    getConnection: jest.fn()
  }
}));

jest.mock('../models/challengeUserMappingModel');

describe('GameEndService', () => {
  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();

    // Mock connection for transactions
    const mockConnection = {
      beginTransaction: jest.fn().mockResolvedValue(undefined),
      query: jest.fn(),
      commit: jest.fn().mockResolvedValue(undefined),
      rollback: jest.fn().mockResolvedValue(undefined),
      release: jest.fn()
    };

    // Set up the mock connection
    pool.getConnection.mockResolvedValue(mockConnection);
  });

  describe('checkAndEndExpiredChallenges', () => {
    test('should end expired challenges', async () => {
      // Arrange
      const mockExpiredChallenges = [
        { id: 1, name: 'Challenge 1', end_date: '2023-01-01' },
        { id: 2, name: 'Challenge 2', end_date: '2023-01-02' }
      ];

      // Mock the query to find expired challenges
      pool.query.mockImplementation((query, params) => {
        if (query.includes('SELECT id, name, end_date')) {
          return [mockExpiredChallenges];
        } else if (query.includes('UPDATE tbl_challenges')) {
          return [{ affectedRows: 1 }];
        } else if (query.includes('UPDATE tbl_challenge_user_mapping')) {
          return [{ affectedRows: 2 }];
        } else if (query.includes('SELECT * FROM tbl_challenges')) {
          const challengeId = params[0];
          if (challengeId === 1) {
            return [[{ id: 1, name: 'Challenge 1', winning_rules: '{"level":3,"winning_points":300,"rank":1}' }]];
          } else if (challengeId === 2) {
            return [[{ id: 2, name: 'Challenge 2', winning_rules: '{"level":3,"winning_points":300,"rank":1}' }]];
          }
        } else if (query.includes('SELECT * FROM tbl_challenge_user_mapping')) {
          return [[]]; // No users in the challenge
        } else if (query.includes('SELECT * FROM tbl_game_winners')) {
          return [[]]; // No existing winners
        } else if (query.includes('INSERT INTO tbl_game_winners')) {
          return [{ affectedRows: 1 }];
        }
        return [[]];
      });

      // Mock updateRanks
      ChallengeUserMappingModel.updateRanks.mockResolvedValue([]);

      // Act
      const result = await GameEndService.checkAndEndExpiredChallenges();

      // Assert
      expect(pool.query).toHaveBeenCalled();
      expect(ChallengeUserMappingModel.updateRanks).toHaveBeenCalledTimes(2);
      expect(result.ended).toBe(2);
      expect(result.total).toBe(2);
    });

    test('should return 0 when no expired challenges found', async () => {
      // Arrange
      pool.query.mockImplementation((query) => {
        if (query.includes('SELECT id, name, end_date')) {
          return [[]]; // No expired challenges
        }
        return [[]];
      });

      // Act
      const result = await GameEndService.checkAndEndExpiredChallenges();

      // Assert
      expect(result.ended).toBe(0);
    });
  });

  describe('endChallenge', () => {
    test('should end a specific challenge', async () => {
      // Arrange
      const challengeId = 123;

      // Mock updateRanks
      const mockRankResults = [
        { id: 1, userId: 101, score: 300, rank: 1, rankChanged: true },
        { id: 2, userId: 102, score: 200, rank: 2, rankChanged: true }
      ];
      ChallengeUserMappingModel.updateRanks.mockResolvedValue(mockRankResults);

      // Reset all mocks
      jest.clearAllMocks();

      // Mock all database queries
      pool.query = jest.fn((query, params) => {
        if (query.includes('SELECT * FROM tbl_challenges')) {
          return [[{ id: challengeId, name: 'Test Challenge', status: 1, winning_rules: '{"level":3,"winning_points":300,"rank":1}' }]];
        } else if (query.includes('UPDATE tbl_challenges')) {
          return [{ affectedRows: 1 }];
        } else if (query.includes('UPDATE tbl_challenge_user_mapping')) {
          return [{ affectedRows: 2 }];
        } else if (query.includes('SELECT * FROM tbl_challenge_user_mapping')) {
          return [[{ userId: 101, score: 300, level: 3, rank: 1 }]];
        } else if (query.includes('SELECT * FROM tbl_game_winners')) {
          return [[]];
        } else if (query.includes('INSERT INTO tbl_game_winners')) {
          return [{ affectedRows: 1 }];
        }
        return [[]];
      });

      // Act
      const result = await GameEndService.endChallenge(challengeId);

      // Assert
      expect(pool.query).toHaveBeenCalledWith(expect.stringContaining('SELECT * FROM tbl_challenges'), [challengeId]);
      expect(pool.query).toHaveBeenCalledWith(expect.stringContaining('UPDATE tbl_challenges'), [challengeId]);
      expect(pool.query).toHaveBeenCalledWith(expect.stringContaining('UPDATE tbl_challenge_user_mapping'), [challengeId]);
      expect(ChallengeUserMappingModel.updateRanks).toHaveBeenCalledWith(challengeId);

      expect(result.success).toBe(true);
      expect(result.challengeId).toBe(challengeId);
      expect(result.rankResults).toEqual(mockRankResults);
    });

    test('should return already ended message if challenge is already ended', async () => {
      // Arrange
      const challengeId = 123;

      // Reset all mocks
      jest.clearAllMocks();

      // Mock the query to get challenge details with status 2 (already ended)
      pool.query = jest.fn((query, params) => {
        if (query.includes('SELECT * FROM tbl_challenges')) {
          return [[{ id: challengeId, name: 'Test Challenge', status: 2, winning_rules: '{"level":3,"winning_points":300,"rank":1}' }]];
        }
        return [[]];
      });

      // Act
      const result = await GameEndService.endChallenge(challengeId);

      // Assert
      expect(result.success).toBe(true);
      expect(result.alreadyEnded).toBe(true);
      expect(result.message).toBe('Challenge is already ended');
    });
  });
});
