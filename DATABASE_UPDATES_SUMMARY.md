# Database Structure Updates Summary

## Overview
This document summarizes all the changes made to the API endpoints and services to align with the updated database structure for the achawach backend application.

## Database Structure Analysis
The database now contains the following tables with their respective columns:

### New Tables Added:
1. **tbl_game_winners** - Tracks game winners
2. **tbl_categories_icons** - Icons for categories
3. **tbl_challenge_categories_icons** - Icons for challenge categories with colors
4. **tbl_questions_categories_translation** - Translations for question categories
5. **tbl_questions_choices_translation** - Translations for question choices
6. **tbl_questions_translation** - Translations for questions
7. **tbl_challenges_translation** - Translations for challenges
8. **tbl_users_admin** - Admin users table
9. **tbl_user_interests** - User interests/categories

### Updated Tables:
1. **tbl_challenges** - Added `reward_rules` and `period_type` columns
2. **tbl_challenge_categories** - Added `color`, `langId`, `iconId` columns
3. **tbl_users** - Added `langId` column
4. **tbl_recent_activity** - Added `langId` column
5. **tbl_challenge_user_mapping** - Already had `rank` column

## New Models Created:

### Core Models:
- `models/gameWinnersModel.js` - Game winners functionality
- `models/usersAdminModel.js` - Admin users with authentication
- `models/userInterestsModel.js` - User interests management

### Icon Models:
- `models/categoriesIconsModel.js` - Categories icons
- `models/challengeCategoriesIconsModel.js` - Challenge categories icons

### Translation Models:
- `models/challengesTranslationModel.js` - Challenge translations
- `models/questionsTranslationModel.js` - Question translations
- `models/questionsChoicesTranslationModel.js` - Question choice translations
- `models/questionsCategoriesTranslationModel.js` - Question category translations

### Updated Models:
- `models/challengeCategoryModel.js` - Added color, langId, iconId support
- `models/userModel.js` - Added langId support
- `models/challengeModel.js` - Added reward_rules and period_type support
- `models/recent_activityModel.js` - Added langId support

## New Services Created:

- `services/gameWinnersService.js` - Game winners business logic
- `services/adminService.js` - Admin authentication and management
- `services/userInterestsService.js` - User interests management
- `services/translationService.js` - Multi-language translation support
- `services/iconsService.js` - Icons management

## New Controllers Created:

- `controllers/gameWinnersController.js` - Game winners API endpoints
- `controllers/adminController.js` - Admin API endpoints
- `controllers/userInterestsController.js` - User interests API endpoints
- `controllers/translationController.js` - Translation API endpoints
- `controllers/iconsController.js` - Icons API endpoints

## New Middleware:

- `middleware/adminAuthMiddleware.js` - Admin authentication middleware

## API Endpoints Added:

### Game Winners Endpoints:
- `GET /api/v1/game-winners` - Get all winners
- `GET /api/v1/game-winners/challenge/:challengeId` - Get winners by challenge
- `GET /api/v1/game-winners/user/:userId` - Get wins by user
- `GET /api/v1/my-wins` - Get current user's wins
- `POST /api/v1/game-winners` - Create winner
- `DELETE /api/v1/game-winners/:winnerId` - Delete winner
- `GET /api/v1/game-winners/check/:userId/:challengeId` - Check winner status
- `GET /api/v1/my-winner-status/:challengeId` - Check current user's winner status

### Admin Endpoints:
- `POST /api/v1/admin/login` - Admin login
- `GET /api/v1/admin/profile` - Get admin profile
- `PUT /api/v1/admin/profile` - Update admin profile
- `GET /api/v1/admin/admins` - Get all admins
- `GET /api/v1/admin/admins/:id` - Get admin by ID
- `POST /api/v1/admin/admins` - Create admin
- `PUT /api/v1/admin/admins/:id` - Update admin
- `DELETE /api/v1/admin/admins/:id` - Delete admin

### User Interests Endpoints:
- `GET /api/v1/user-interests/:userId` - Get user interests
- `GET /api/v1/my-interests` - Get current user's interests
- `GET /api/v1/user-interests/:userId/details` - Get user interests with details
- `GET /api/v1/my-interests/details` - Get current user's interests with details
- `PUT /api/v1/user-interests/:userId` - Update user interests
- `PUT /api/v1/my-interests` - Update current user's interests
- `POST /api/v1/user-interests` - Create user interests
- `POST /api/v1/my-interests` - Create current user's interests
- `DELETE /api/v1/user-interests/:userId` - Delete user interests
- `DELETE /api/v1/my-interests` - Delete current user's interests
- `POST /api/v1/user-interests/:userId/categories` - Add category to user interests
- `POST /api/v1/my-interests/categories` - Add category to current user's interests
- `DELETE /api/v1/user-interests/:userId/categories/:categoryId` - Remove category from user interests
- `DELETE /api/v1/my-interests/categories/:categoryId` - Remove category from current user's interests

### Translation Endpoints:
#### Challenge Translations:
- `GET /api/v1/translations/challenges/:challengeId` - Get challenge translations
- `GET /api/v1/translations/challenges/:challengeId/:langId` - Get specific challenge translation
- `POST /api/v1/translations/challenges/:challengeId` - Create challenge translation
- `PUT /api/v1/translations/challenges/:challengeId/:langId` - Update challenge translation
- `DELETE /api/v1/translations/challenges/:translationId` - Delete challenge translation

#### Question Translations:
- `GET /api/v1/translations/questions/:questionId` - Get question translations
- `GET /api/v1/translations/questions/:questionId/:langId` - Get specific question translation
- `POST /api/v1/translations/questions/:questionId` - Create question translation
- `PUT /api/v1/translations/questions/:questionId/:langId` - Update question translation

#### Question Choice Translations:
- `GET /api/v1/translations/choices/:choiceId` - Get choice translations
- `GET /api/v1/translations/questions/:questionId/choices` - Get question choices with translations
- `POST /api/v1/translations/choices/:choiceId` - Create choice translation
- `PUT /api/v1/translations/choices/:choiceId/:langId` - Update choice translation

#### Question Category Translations:
- `GET /api/v1/translations/question-categories` - Get category translations
- `POST /api/v1/translations/question-categories` - Create category translation
- `PUT /api/v1/translations/question-categories/:categoryIconId/:langId` - Update category translation

#### Bulk Translation Operations:
- `GET /api/v1/translations/challenges` - Get challenges with translations
- `GET /api/v1/translations/questions` - Get questions with translations

### Icons Endpoints:
#### Categories Icons:
- `GET /api/v1/icons/categories` - Get all categories icons
- `GET /api/v1/icons/categories/:id` - Get categories icon by ID
- `POST /api/v1/icons/categories` - Create categories icon
- `PUT /api/v1/icons/categories/:id` - Update categories icon
- `DELETE /api/v1/icons/categories/:id` - Delete categories icon

#### Challenge Categories Icons:
- `GET /api/v1/icons/challenge-categories` - Get all challenge categories icons
- `GET /api/v1/icons/challenge-categories/:id` - Get challenge categories icon by ID
- `POST /api/v1/icons/challenge-categories` - Create challenge categories icon
- `PUT /api/v1/icons/challenge-categories/:id` - Update challenge categories icon
- `DELETE /api/v1/icons/challenge-categories/:id` - Delete challenge categories icon

## Key Features Implemented:

1. **Multi-language Support**: Complete translation system for challenges, questions, choices, and categories
2. **Admin Management**: Full admin user management with separate authentication
3. **Game Winners Tracking**: Comprehensive winner tracking and management
4. **User Interests**: User category preferences management
5. **Icons Management**: Centralized icon management for categories
6. **Enhanced Models**: Updated existing models to support new database columns

## Authentication:
- Regular user endpoints use `authenticateToken` middleware
- Admin endpoints use `authenticateAdmin` middleware for enhanced security
- Admin tokens include `type: 'admin'` for differentiation

## Database Compatibility:
All new models and services are designed to work with the existing database structure while adding support for the new tables and columns identified in the database schema analysis.

## Testing Recommendations:
1. Test all new endpoints with proper authentication
2. Verify translation functionality with different language IDs
3. Test admin authentication and authorization
4. Validate user interests CRUD operations
5. Test game winners functionality
6. Verify icon management operations
