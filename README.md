# Achawach Backend

A Node.js backend application with Express, Socket.IO, and MySQL.

## Prerequisites

- [Docker](https://www.docker.com/get-started)
- [Docker Compose](https://docs.docker.com/compose/install/)

## Getting Started with Docker

### Setup Environment Variables

1. Copy the example environment file:

   ```bash
   cp .env.example .env
   ```

2. Edit the `.env` file and update the values as needed.

### Running the Application

#### Using Docker Compose Directly

1. Build and start the containers:

   ```bash
   docker-compose up -d
   ```

2. The application will be available at [http://localhost:3000](http://localhost:3000)

3. To view logs:

   ```bash
   docker-compose logs -f app
   ```

4. To stop the application:

   ```bash
   docker-compose down
   ```

#### Using the Helper Script

Make the script executable:

```bash
chmod +x docker-scripts.sh
```

Available commands:

```bash
./docker-scripts.sh build    # Build the Docker image
./docker-scripts.sh up       # Start the containers
./docker-scripts.sh down     # Stop the containers
./docker-scripts.sh logs     # Show logs from the app container
./docker-scripts.sh restart  # Restart the containers
./docker-scripts.sh shell    # Open a shell in the app container
./docker-scripts.sh mysql    # Open MySQL CLI in the database container
./docker-scripts.sh help     # Display help message
```

## Development Without Docker

### Local Development Requirements

- Node.js (v14 or higher)
- MySQL

### Setup

1. Install dependencies:

   ```bash
   npm install
   ```

2. Set up environment variables:

   ```bash
   cp .env.example .env
   ```

   Edit the `.env` file with your local MySQL credentials.

3. Start the application:

   ```bash
   node server.js
   ```

## Testing

Run tests:

```bash
npm test
```

Run tests with watch mode:

```bash
npm run test:watch
```

Run tests with coverage:

```bash
npm run test:coverage
```
