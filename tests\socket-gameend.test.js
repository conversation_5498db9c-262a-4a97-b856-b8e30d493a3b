const SocketManager = require('../config/socket');
const GameEndService = require('../services/gameEndService');

// Mock dependencies
jest.mock('socket.io', () => {
  const Server = jest.fn().mockImplementation(() => ({
    use: jest.fn(),
    on: jest.fn(),
    to: jest.fn().mockReturnThis(),
    emit: jest.fn(),
    sockets: {
      adapter: {
        rooms: new Map()
      }
    }
  }));
  return { Server };
});

jest.mock('../services/gameEndService');
jest.mock('../config/config', () => ({
  pool: {
    query: jest.fn()
  }
}));

describe('SocketManager - Game End Functionality', () => {
  let socketManager;
  let mockServer;
  let mockIo;

  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();

    // Use fake timers
    jest.useFakeTimers();

    // Mock socket.io Server
    mockIo = {
      use: jest.fn(),
      on: jest.fn(),
      to: jest.fn().mockReturnThis(),
      emit: jest.fn(),
      sockets: {
        adapter: {
          rooms: new Map()
        }
      }
    };
    jest.requireMock('socket.io').Server.mockImplementation(() => mockIo);

    // Mock server
    mockServer = {};

    // Create SocketManager instance
    socketManager = new SocketManager(mockServer);
  });

  afterEach(() => {
    // Restore real timers
    jest.useRealTimers();
  });

  describe('startPeriodicEndGame', () => {
    test('should start a periodic interval to check for expired challenges', () => {
      // Arrange
      const intervalId = 12345;
      jest.spyOn(global, 'setInterval').mockReturnValue(intervalId);

      // Act
      const result = socketManager.startPeriodicEndGame();

      // Assert
      expect(result).toBe(intervalId);
      expect(setInterval).toHaveBeenCalledWith(expect.any(Function), socketManager.GAME_END_INTERVAL);
      expect(socketManager.gameEndIntervals.get('global')).toBe(intervalId);
    });

    test('should not create a new interval if one already exists', () => {
      // Arrange
      const existingIntervalId = 12345;
      socketManager.gameEndIntervals.set('global', existingIntervalId);
      jest.spyOn(global, 'setInterval');

      // Act
      const result = socketManager.startPeriodicEndGame();

      // Assert
      expect(result).toBe(existingIntervalId);
      expect(setInterval).not.toHaveBeenCalled();
    });

    test('should check active games for ended challenges when interval fires', () => {
      // Arrange
      const mockQueryResult = [
        [{ id: 1, challenge_id: 123, userId: 456, status: 2, name: 'Challenge 1', challenge_name: 'Test Challenge' }]
      ];
      const mockPool = jest.requireMock('../config/config').pool;
      mockPool.query.mockResolvedValue(mockQueryResult);

      // Add a mock active game
      const mockSocket = { emit: jest.fn() };
      socketManager.activeGames.set('456-123', {
        socket: mockSocket,
        userId: 456,
        challengeId: 123
      });

      // Act
      socketManager.startPeriodicEndGame();
      jest.advanceTimersByTime(socketManager.GAME_END_INTERVAL);

      // Assert
      expect(mockPool.query).toHaveBeenCalledWith(
        expect.stringContaining('SELECT m.*, c.name as challenge_name'),
        [456, 123]
      );
    });

    test('should emit challengeEnded event to users with ended challenges', async () => {
      // Arrange
      const mockQueryResult = [
        [{ id: 1, challenge_id: 123, userId: 456, status: 2, name: 'Challenge 1', challenge_name: 'Test Challenge' }]
      ];
      const mockPool = jest.requireMock('../config/config').pool;
      mockPool.query.mockResolvedValue(mockQueryResult);

      // Add a mock active game
      const mockSocket = { emit: jest.fn() };
      socketManager.activeGames.set('456-123', {
        socket: mockSocket,
        userId: 456,
        challengeId: 123
      });

      // Act
      socketManager.startPeriodicEndGame();

      // Advance timers and wait for promises to resolve
      jest.advanceTimersByTime(socketManager.GAME_END_INTERVAL);
      await Promise.resolve(); // Wait for the async function in setInterval to complete

      // Assert
      expect(mockSocket.emit).toHaveBeenCalledWith('challengeEnded', {
        challengeId: 123,
        message: 'Challenge "Test Challenge" has ended.',
        status: 2
      });
    });

    test('should handle errors and increment error count', async () => {
      // Arrange
      const error = new Error('Test error');
      const mockPool = jest.requireMock('../config/config').pool;
      mockPool.query.mockRejectedValue(error);
      jest.spyOn(console, 'error').mockImplementation(() => {});

      // Add a mock active game to trigger the query
      const mockSocket = { emit: jest.fn() };
      socketManager.activeGames.set('456-123', {
        socket: mockSocket,
        userId: 456,
        challengeId: 123
      });

      // Act
      socketManager.startPeriodicEndGame();

      // Advance timers and wait for promises to resolve
      jest.advanceTimersByTime(socketManager.GAME_END_INTERVAL);
      await Promise.resolve(); // Wait for the async function in setInterval to complete

      // Assert
      expect(console.error).toHaveBeenCalled();
      expect(console.error.mock.calls[0][0]).toContain('Error checking ended status for user game 456-123');
    });

    test('should stop interval after MAX_ERRORS consecutive errors', async () => {
      // Arrange
      const error = new Error('Test error');
      // Mock the entire interval function to throw an error
      jest.spyOn(global, 'setInterval').mockImplementation((fn) => {
        // Call the function immediately to simulate an error
        fn().catch(() => {}); // Catch to prevent unhandled rejection
        return 12345; // Return a fake interval ID
      });
      jest.spyOn(console, 'error').mockImplementation(() => {});
      jest.spyOn(socketManager, 'stopPeriodicEndGame');

      // Act
      socketManager.startPeriodicEndGame();

      // Simulate 5 consecutive errors by manually calling the error handler
      for (let i = 0; i < 5; i++) {
        // Access the private errorCount variable by calling the interval function again
        setInterval.mock.calls[0][0]().catch(() => {});
      }

      // Assert
      expect(socketManager.stopPeriodicEndGame).toHaveBeenCalled();
    });
  });

  describe('stopPeriodicEndGame', () => {
    test('should clear the interval and remove it from the map', () => {
      // Arrange
      const intervalId = 12345;
      socketManager.gameEndIntervals.set('global', intervalId);
      jest.spyOn(global, 'clearInterval');

      // Act
      const result = socketManager.stopPeriodicEndGame();

      // Assert
      expect(result).toBe(true);
      expect(clearInterval).toHaveBeenCalledWith(intervalId);
      expect(socketManager.gameEndIntervals.has('global')).toBe(false);
    });

    test('should return false if no interval exists', () => {
      // Act
      const result = socketManager.stopPeriodicEndGame();

      // Assert
      expect(result).toBe(false);
      expect(clearInterval).not.toHaveBeenCalled();
    });
  });
});
