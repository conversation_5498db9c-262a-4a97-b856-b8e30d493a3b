const ChallengeCategory = require('../models/challengeCategoryModel');
const { AppError } = require('../middleware/errorMiddleware');

class ChallengeCategoryService {
    static async getAllCategories() {
        return await ChallengeCategory.findAll();
    }

    static async getCategoryById(id) {
        const category = await ChallengeCategory.findById(id);
        if (!category) {
            throw new AppError('Category not found', 404);
        }
        return category;
    }

    static async createCategory(categoryData) {
        // Validate category data
        if (!categoryData.name) {
            throw new AppError('Category name is required', 400);
        }

        return await ChallengeCategory.create(categoryData);
    }

    static async updateCategory(id, categoryData) {
        // Check if category exists
        const category = await this.getCategoryById(id);

        // Update category
        return await ChallengeCategory.update(id, {
            name: categoryData.name || category.name,
            icon: categoryData.icon || category.icon
        });
    }

    static async deleteCategory(id) {
        // Check if category exists
        await this.getCategoryById(id);
        
        // Delete category
        const deleted = await ChallengeCategory.delete(id);
        if (!deleted) {
            throw new AppError('Failed to delete category', 500);
        }
        return true;
    }
}

module.exports = ChallengeCategoryService; 