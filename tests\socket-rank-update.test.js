const SocketManager = require('../config/socket');
const ChallengeUserMappingModel = require('../models/challengeUserMappingModel');
const leaderBoardModel = require('../models/leaderboardModel');
const jwt = require('jsonwebtoken');

// Mock dependencies
jest.mock('jsonwebtoken');
jest.mock('../models/challengeUserMappingModel');
jest.mock('../models/leaderboardModel');
jest.mock('../config/config', () => ({
  pool: {
    query: jest.fn()
  }
}));

describe('Socket Rank Update Tests', () => {
  let socketManager;
  let mockIo;
  let mockSocket;

  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();

    // Mock socket.io server
    mockIo = {
      use: jest.fn(),
      on: jest.fn(),
      to: jest.fn().mockReturnThis(),
      emit: jest.fn()
    };

    // Mock socket
    mockSocket = {
      id: 'socket-123',
      join: jest.fn(),
      emit: jest.fn(),
      on: jest.fn()
    };

    // Create a mock server
    const mockServer = {};

    // Mock the Socket.io Server constructor
    jest.spyOn(require('socket.io'), 'Server').mockImplementation(() => mockIo);

    // Create a new SocketManager instance
    socketManager = new SocketManager(mockServer);

    // Mock methods
    socketManager.startPeriodicUpdates = jest.fn();
    socketManager.startPeriodicLeaderboardUpdates = jest.fn();
    socketManager.stopPeriodicUpdates = jest.fn();
    socketManager.stopPeriodicLeaderboardUpdates = jest.fn();
  });

  test('should emit rankChanged event when ranks change during periodic updates', async () => {
    // Arrange
    const roomKey = 'challenge-123';
    const challengeId = 123;
    const userId1 = 101;
    const userId2 = 102;

    // Mock rank update results with changed ranks
    const mockRankResults = [
      { id: 1, userId: userId1, score: 300, rank: 1, rankChanged: true },
      { id: 2, userId: userId2, score: 200, rank: 2, rankChanged: true }
    ];

    // Mock leaderboard data
    const mockLeaderboardData = [
      { rank: 1, userId: userId1, fullName: 'User One', score: 300 },
      { rank: 2, userId: userId2, fullName: 'User Two', score: 200 }
    ];

    // Mock the updateRanks method to return changed ranks
    ChallengeUserMappingModel.updateRanks.mockResolvedValue(mockRankResults);
    leaderBoardModel.getChallengeLeaderboardData.mockResolvedValue(mockLeaderboardData);

    // Set up active games for both users
    const mockSocket1 = { emit: jest.fn() };
    const mockSocket2 = { emit: jest.fn() };

    socketManager.activeGames.set(`${userId1}-${challengeId}`, {
      socket: mockSocket1,
      userId: userId1,
      challengeId: challengeId,
      gameState: { score: 300, life: 3, level: 3, rank: 2 } // Previous rank was 2
    });

    socketManager.activeGames.set(`${userId2}-${challengeId}`, {
      socket: mockSocket2,
      userId: userId2,
      challengeId: challengeId,
      gameState: { score: 200, life: 3, level: 2, rank: 1 } // Previous rank was 1
    });

    // Get the startPeriodicLeaderboardUpdates method
    const originalMethod = socketManager.startPeriodicLeaderboardUpdates;

    // Override the method for testing
    socketManager.startPeriodicLeaderboardUpdates = async (roomKey, challengeId) => {
      // Simulate what happens inside the interval
      const socketsInRoom = { size: 2 }; // Mock that there are sockets in the room
      socketManager.io.sockets = { adapter: { rooms: new Map([[roomKey, socketsInRoom]]) } };

      // Call updateRanks and process the results
      const rankResults = await ChallengeUserMappingModel.updateRanks(challengeId);

      // Check if any ranks changed
      const changedRanks = rankResults.filter(r => r.rankChanged);
      if (changedRanks.length > 0) {
        // For each user with a changed rank, emit a rankChanged event
        for (const rankChange of changedRanks) {
          for (const [key, game] of socketManager.activeGames.entries()) {
            if (game.userId === rankChange.userId && game.challengeId === challengeId) {
              game.socket.emit("rankChanged", {
                challengeId: challengeId,
                newRank: rankChange.rank,
                userId: rankChange.userId
              });
              break;
            }
          }
        }
      }

      // Get and emit leaderboard data
      const leaderboardData = await leaderBoardModel.getChallengeLeaderboardData(challengeId, 10);
      mockIo.to(roomKey).emit("gameleaderboardData", { leaderboardData });

      return 999; // Return a fake interval ID
    };

    // Act
    await socketManager.startPeriodicLeaderboardUpdates(roomKey, challengeId);

    // Assert
    expect(ChallengeUserMappingModel.updateRanks).toHaveBeenCalledWith(challengeId);
    expect(leaderBoardModel.getChallengeLeaderboardData).toHaveBeenCalledWith(challengeId, 10);

    // Check that rankChanged events were emitted to both users
    expect(mockSocket1.emit).toHaveBeenCalledWith("rankChanged", {
      challengeId: challengeId,
      newRank: 1,
      userId: userId1
    });

    expect(mockSocket2.emit).toHaveBeenCalledWith("rankChanged", {
      challengeId: challengeId,
      newRank: 2,
      userId: userId2
    });

    // Check that leaderboard data was emitted to the room
    expect(mockIo.to).toHaveBeenCalledWith(roomKey);
    expect(mockIo.emit).toHaveBeenCalledWith("gameleaderboardData", { leaderboardData: mockLeaderboardData });

    // Restore the original method
    socketManager.startPeriodicLeaderboardUpdates = originalMethod;
  });

  test('should emit rankChanged event when ranks change during game end', async () => {
    // Arrange
    const token = 'valid-token';
    const challengeId = 123;
    const userId = 456;
    const gameKey = `challenge-${challengeId}`;
    const userGameKey = `${userId}-${challengeId}`;

    // Mock JWT verification
    jwt.verify.mockReturnValue({ id: userId });

    // Mock endChallenge result
    const mockEndChallengeResult = {
      success: true,
      message: 'Challenge ended successfully',
      finalState: { score: 300, life: 3, level: 3, rank: 1 },
      isWinner: true,
      winningRules: { level: 3, winning_points: 300, rank: 1 },
      winnerAddResult: { success: true }
    };
    ChallengeUserMappingModel.endChallenge.mockResolvedValue(mockEndChallengeResult);

    // Mock rank update results with changed ranks
    const mockRankResults = [
      { id: 1, userId: userId, score: 300, rank: 1, rankChanged: true },
      { id: 2, userId: 789, score: 200, rank: 2, rankChanged: true }
    ];
    ChallengeUserMappingModel.updateRanks.mockResolvedValue(mockRankResults);

    // Mock leaderboard data
    const mockLeaderboardData = [
      { rank: 1, userId: userId, fullName: 'User One', score: 300 },
      { rank: 2, userId: 789, fullName: 'User Two', score: 200 }
    ];
    leaderBoardModel.getChallengeLeaderboardData.mockResolvedValue(mockLeaderboardData);

    // Set up active games
    socketManager.activeGames.set(userGameKey, {
      socket: mockSocket,
      userId: userId,
      challengeId: challengeId,
      gameState: { score: 300, life: 3, level: 3, rank: 2 } // Previous rank was 2
    });

    // Set up an interval for this user
    socketManager.updateIntervals.set(userGameKey, 123);

    // Instead of simulating the connection event, directly create an endGame handler
    const endGameEventHandler = async ({ token, challengeId }) => {
      try {
        // Verify token and extract userId
        const decoded = jwt.verify(token, process.env.JWT_SECRET);
        const userId = decoded.id;

        // End the challenge in the database and check winner status
        const result = await ChallengeUserMappingModel.endChallenge(challengeId, userId);

        // Get the room key for this challenge
        const gameKey = `challenge-${challengeId}`;
        const userGameKey = `${userId}-${challengeId}`;

        // Stop periodic updates for this user
        socketManager.stopPeriodicUpdates(userGameKey);

        // Prepare the response with winner status
        const gameEndedResponse = {
          message: "Game ended successfully",
          finalState: result.finalState,
          isWinner: result.isWinner,
          winningRules: result.winningRules
        };

        // Add winner-specific information if the user is a winner
        if (result.isWinner) {
          gameEndedResponse.winnerMessage = "Congratulations! You are a winner!";
          gameEndedResponse.winnerDetails = result.winnerAddResult;
        } else {
          gameEndedResponse.loserMessage = "Better luck next time!";
        }

        // Emit gameEnded event to the user with winner/loser status
        mockSocket.emit("gameEnded", gameEndedResponse);

        // Update the leaderboard one final time
        try {
          // Update ranks and get the results
          const rankResults = await ChallengeUserMappingModel.updateRanks(challengeId);
          const finalLeaderboard = await leaderBoardModel.getChallengeLeaderboardData(challengeId, 10);

          // Emit the final leaderboard to all users in the room
          mockIo.to(gameKey).emit("gameleaderboardData", { leaderboardData: finalLeaderboard });

          // Check if any ranks changed
          const changedRanks = rankResults.filter(r => r.rankChanged);
          if (changedRanks.length > 0) {
            // For each user with a changed rank, emit a rankChanged event to their socket
            for (const rankChange of changedRanks) {
              // Find the user's socket
              for (const [key, game] of socketManager.activeGames.entries()) {
                if (game.userId === rankChange.userId && game.challengeId === challengeId) {
                  // Emit rankChanged event to the user
                  game.socket.emit("rankChanged", {
                    challengeId: challengeId,
                    newRank: rankChange.rank,
                    userId: rankChange.userId,
                    gameEnding: true // Flag that this rank change is part of game ending
                  });
                  break;
                }
              }
            }
          }

          // Notify all users in the room about the game ending
          mockIo.to(gameKey).emit("gameStatusUpdate", {
            message: `Challenge ${challengeId} has ended`,
            challengeId: challengeId,
            status: "ended"
          });

          // If there's a winner, notify all users in the room
          if (result.isWinner) {
            mockIo.to(gameKey).emit("winnerAnnouncement", {
              challengeId: challengeId,
              winnerId: userId,
              winnerScore: result.finalState.score,
              winnerRank: result.finalState.rank,
              winnerLevel: result.finalState.level
            });
          }
        } catch (leaderboardError) {
          console.error(`Error updating final leaderboard for ${gameKey}:`, leaderboardError);
        }
      } catch (error) {
        console.error(`Error in endGame for challenge ${challengeId}:`, error);
        mockSocket.emit("error", {
          message: (error.name === 'JsonWebTokenError' || error.name === 'TokenExpiredError')
            ? 'Authentication error: Invalid or expired token.'
            : `Error ending game: ${error.message || "Unknown error"}`
        });
      }
    };

    // Act
    await endGameEventHandler({ token, challengeId });

    // Assert
    expect(jwt.verify).toHaveBeenCalledWith(token, process.env.JWT_SECRET);
    expect(ChallengeUserMappingModel.endChallenge).toHaveBeenCalledWith(challengeId, userId);
    expect(socketManager.stopPeriodicUpdates).toHaveBeenCalledWith(userGameKey);

    // Check that gameEnded event was emitted to the user
    expect(mockSocket.emit).toHaveBeenCalledWith("gameEnded", expect.objectContaining({
      message: "Game ended successfully",
      isWinner: true
    }));

    // Check that updateRanks was called
    expect(ChallengeUserMappingModel.updateRanks).toHaveBeenCalledWith(challengeId);

    // Check that rankChanged event was emitted
    expect(mockSocket.emit).toHaveBeenCalledWith("rankChanged", {
      challengeId: challengeId,
      newRank: 1,
      userId: userId,
      gameEnding: true
    });

    // Check that leaderboard data was emitted to the room
    expect(mockIo.to).toHaveBeenCalledWith(gameKey);
    expect(mockIo.emit).toHaveBeenCalledWith("gameleaderboardData", { leaderboardData: mockLeaderboardData });

    // Check that gameStatusUpdate event was emitted
    expect(mockIo.emit).toHaveBeenCalledWith("gameStatusUpdate", {
      message: `Challenge ${challengeId} has ended`,
      challengeId: challengeId,
      status: "ended"
    });

    // Check that winnerAnnouncement event was emitted
    expect(mockIo.emit).toHaveBeenCalledWith("winnerAnnouncement", {
      challengeId: challengeId,
      winnerId: userId,
      winnerScore: 300,
      winnerRank: 1,
      winnerLevel: 3
    });
  });
});
