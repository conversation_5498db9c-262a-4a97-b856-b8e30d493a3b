const { pool } = require('../config/config');

class DatabaseMigrationService {
    /**
     * Create default translation records for challenges that don't have them
     * This is needed because foreign keys point to translation tables
     */
    static async ensureChallengeTranslations() {
        try {
            // Get challenges without translations
            const [challengesWithoutTranslations] = await pool.query(`
                SELECT c.id, c.winning_rules, c.reward
                FROM tbl_challenges c
                LEFT JOIN tbl_challenges_translation ct ON c.id = ct.challengeId
                WHERE ct.id IS NULL
            `);

            if (challengesWithoutTranslations.length === 0) {
                console.log('All challenges already have translations');
                return;
            }

            // Get default language ID (assuming 1 is default, adjust as needed)
            const [languages] = await pool.query('SELECT id FROM tbl_language ORDER BY id LIMIT 1');
            const defaultLangId = languages[0]?.id || 1;

            // Get the next available ID for tbl_challenges_translation
            const [maxIdResult] = await pool.query('SELECT COALESCE(MAX(id), 0) + 1 as nextId FROM tbl_challenges_translation');
            let nextId = maxIdResult[0].nextId;

            // Create default translations for challenges without them
            for (const challenge of challengesWithoutTranslations) {
                await pool.query(`
                    INSERT INTO tbl_challenges_translation (id, challengeId, langId, name, description, createdAt, updatedAt)
                    VALUES (?, ?, ?, ?, ?, NOW(), NOW())
                `, [
                    nextId++, // Provide explicit ID
                    challenge.id,
                    defaultLangId,
                    `Challenge ${challenge.id}`, // Default name
                    challenge.winning_rules || 'No description available' // Use winning_rules as description
                ]);
            }

            console.log(`Created translations for ${challengesWithoutTranslations.length} challenges`);
        } catch (error) {
            console.error('Error ensuring challenge translations:', error);
            throw error;
        }
    }

    /**
     * Create default translation records for questions that don't have them
     */
    static async ensureQuestionTranslations() {
        try {
            // Get questions without translations
            const [questionsWithoutTranslations] = await pool.query(`
                SELECT q.id, q.question_text
                FROM tbl_questions q
                LEFT JOIN tbl_questions_translation qt ON q.id = qt.questionId
                WHERE qt.id IS NULL
            `);

            if (questionsWithoutTranslations.length === 0) {
                console.log('All questions already have translations');
                return;
            }

            // Get default language ID
            const [languages] = await pool.query('SELECT id FROM tbl_language ORDER BY id LIMIT 1');
            const defaultLangId = languages[0]?.id || 1;

            // Get the next available ID for tbl_questions_translation
            const [maxIdResult] = await pool.query('SELECT COALESCE(MAX(id), 0) + 1 as nextId FROM tbl_questions_translation');
            let nextId = maxIdResult[0].nextId;

            // Create default translations for questions without them
            for (const question of questionsWithoutTranslations) {
                await pool.query(`
                    INSERT INTO tbl_questions_translation (id, questionId, langId, question_text, createdAt, updatedAt)
                    VALUES (?, ?, ?, ?, NOW(), NOW())
                `, [
                    nextId++, // Provide explicit ID
                    question.id,
                    defaultLangId,
                    question.question_text || `Question ${question.id}`
                ]);
            }

            console.log(`Created translations for ${questionsWithoutTranslations.length} questions`);
        } catch (error) {
            console.error('Error ensuring question translations:', error);
            throw error;
        }
    }

    /**
     * Get the translation ID for a challenge (needed for foreign key operations)
     */
    static async getChallengeTranslationId(challengeId, langId = null) {
        try {
            // If no langId provided, get the first available translation
            let query, params;
            if (langId) {
                query = 'SELECT id FROM tbl_challenges_translation WHERE challengeId = ? AND langId = ?';
                params = [challengeId, langId];
            } else {
                query = 'SELECT id FROM tbl_challenges_translation WHERE challengeId = ? ORDER BY id LIMIT 1';
                params = [challengeId];
            }

            const [rows] = await pool.query(query, params);
            return rows[0]?.id || null;
        } catch (error) {
            console.error('Error getting challenge translation ID:', error);
            return null;
        }
    }

    /**
     * Get the translation ID for a question (needed for foreign key operations)
     */
    static async getQuestionTranslationId(questionId, langId = null) {
        try {
            let query, params;
            if (langId) {
                query = 'SELECT id FROM tbl_questions_translation WHERE questionId = ? AND langId = ?';
                params = [questionId, langId];
            } else {
                query = 'SELECT id FROM tbl_questions_translation WHERE questionId = ? ORDER BY id LIMIT 1';
                params = [questionId];
            }

            const [rows] = await pool.query(query, params);
            return rows[0]?.id || null;
        } catch (error) {
            console.error('Error getting question translation ID:', error);
            return null;
        }
    }

    /**
     * Initialize all required translations
     */
    static async initializeTranslations() {
        try {
            console.log('Initializing database translations...');
            await this.ensureChallengeTranslations();
            await this.ensureQuestionTranslations();
            console.log('Database translations initialized successfully');
        } catch (error) {
            console.error('Error initializing translations:', error);
            throw error;
        }
    }

    /**
     * Check if the database has the correct foreign key structure
     */
    static async checkForeignKeyStructure() {
        try {
            const [fkInfo] = await pool.query(`
                SELECT 
                    TABLE_NAME, 
                    COLUMN_NAME, 
                    CONSTRAINT_NAME, 
                    REFERENCED_TABLE_NAME, 
                    REFERENCED_COLUMN_NAME 
                FROM information_schema.KEY_COLUMN_USAGE 
                WHERE TABLE_SCHEMA = DATABASE() 
                AND REFERENCED_TABLE_NAME IS NOT NULL
                AND TABLE_NAME IN (
                    'tbl_challenge_user_mapping', 
                    'tbl_asked_questions', 
                    'tbl_questions_choices', 
                    'tbl_game_winners'
                )
            `);

            const issues = [];
            
            for (const fk of fkInfo) {
                if (fk.TABLE_NAME === 'tbl_challenge_user_mapping' && 
                    fk.COLUMN_NAME === 'challenge_id' && 
                    fk.REFERENCED_TABLE_NAME === 'tbl_challenges_translation') {
                    issues.push('challenge_user_mapping.challenge_id references translation table instead of main table');
                }
                
                if (fk.TABLE_NAME === 'tbl_asked_questions' && 
                    fk.COLUMN_NAME === 'qId' && 
                    fk.REFERENCED_TABLE_NAME === 'tbl_questions_translation') {
                    issues.push('asked_questions.qId references translation table instead of main table');
                }
                
                if (fk.TABLE_NAME === 'tbl_questions_choices' && 
                    fk.COLUMN_NAME === 'qId' && 
                    fk.REFERENCED_TABLE_NAME === 'tbl_questions_translation') {
                    issues.push('questions_choices.qId references translation table instead of main table');
                }
                
                if (fk.TABLE_NAME === 'tbl_game_winners' && 
                    fk.COLUMN_NAME === 'challengeId' && 
                    fk.REFERENCED_TABLE_NAME === 'tbl_challenges_translation') {
                    issues.push('game_winners.challengeId references translation table instead of main table');
                }
            }

            if (issues.length > 0) {
                console.warn('Foreign key structure issues detected:');
                issues.forEach(issue => console.warn(`- ${issue}`));
                return false;
            }

            return true;
        } catch (error) {
            console.error('Error checking foreign key structure:', error);
            return false;
        }
    }
}

module.exports = DatabaseMigrationService;
