const { pool } = require('../config/config');
const leaderBoardModel = require('../models/leaderboardModel');

// Mock the database pool
jest.mock('../config/config', () => ({
  pool: {
    query: jest.fn()
  }
}));

describe('leaderBoardModel', () => {
  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks();
  });

  describe('getChallengeLeaderboardData', () => {
    test('should return leaderboard data with ranks from database', async () => {
      // Arrange
      const challengeId = 123;
      const limit = 10;
      
      // Mock database response with rank field
      const mockDbResponse = [
        { 
          userId: 101, 
          fullName: 'User One', 
          avatar: null, 
          profile_image_path: '/images/user1.jpg',
          isProfileImageAvailable: true,
          score: 300, 
          rank: 1 
        },
        { 
          userId: 102, 
          fullName: 'User Two', 
          avatar: '/avatars/default.png', 
          profile_image_path: null,
          isProfileImageAvailable: false,
          score: 200, 
          rank: 2 
        },
        { 
          userId: 103, 
          fullName: 'User Three', 
          avatar: null, 
          profile_image_path: null,
          isProfileImageAvailable: false,
          score: 100, 
          rank: 3 
        }
      ];
      
      pool.query.mockResolvedValueOnce([mockDbResponse]);

      // Act
      const result = await leaderBoardModel.getChallengeLeaderboardData(challengeId, limit);

      // Assert
      // Should query with correct parameters
      expect(pool.query).toHaveBeenCalledWith(expect.any(String), [challengeId, limit]);
      
      // Should return properly formatted leaderboard data with ranks
      expect(result).toEqual([
        { 
          rank: 1, 
          userId: 101, 
          fullName: 'User One', 
          isProfileImageAvailable: true,
          avatar: '/images/user1.jpg', 
          score: 300 
        },
        { 
          rank: 2, 
          userId: 102, 
          fullName: 'User Two', 
          isProfileImageAvailable: false,
          avatar: '/avatars/default.png', 
          score: 200 
        },
        { 
          rank: 3, 
          userId: 103, 
          fullName: 'User Three', 
          isProfileImageAvailable: false,
          avatar: null, 
          score: 100 
        }
      ]);
    });

    test('should default rank to 0 if not provided in database', async () => {
      // Arrange
      const challengeId = 123;
      const limit = 10;
      
      // Mock database response with missing rank field
      const mockDbResponse = [
        { 
          userId: 101, 
          fullName: 'User One', 
          avatar: null, 
          profile_image_path: '/images/user1.jpg',
          isProfileImageAvailable: true,
          score: 300,
          rank: null // Rank is null
        }
      ];
      
      pool.query.mockResolvedValueOnce([mockDbResponse]);

      // Act
      const result = await leaderBoardModel.getChallengeLeaderboardData(challengeId, limit);

      // Assert
      expect(result[0].rank).toBe(0); // Should default to 0
    });

    test('should handle database errors properly', async () => {
      // Arrange
      const challengeId = 123;
      const limit = 10;
      const errorMessage = 'Database error';
      
      // Mock database error
      pool.query.mockRejectedValueOnce(new Error(errorMessage));

      // Act & Assert
      await expect(leaderBoardModel.getChallengeLeaderboardData(challengeId, limit))
        .rejects.toThrow(errorMessage);
    });

    test('should use default limit of 10 if invalid limit provided', async () => {
      // Arrange
      const challengeId = 123;
      const invalidLimit = 'not-a-number';
      
      // Mock database response
      pool.query.mockResolvedValueOnce([[]]);

      // Act
      await leaderBoardModel.getChallengeLeaderboardData(challengeId, invalidLimit);

      // Assert
      // Should use default limit of 10
      expect(pool.query).toHaveBeenCalledWith(expect.any(String), [challengeId, 10]);
    });
  });
});
