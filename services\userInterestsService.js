const UserInterest = require('../models/userInterestsModel');
const { AppError } = require('../middleware/errorMiddleware');

class UserInterestsService {
    static async getUserInterests(userId) {
        try {
            const interests = await UserInterest.findByUserId(userId);
            if (!interests) {
                return { userId, categories: [] };
            }
            
            return {
                id: interests.id,
                userId: interests.userId,
                categories: interests.getParsedCategories(),
                createdAt: interests.createdAt,
                updatedAt: interests.updatedAt
            };
        } catch (error) {
            throw new AppError('Error fetching user interests', 500);
        }
    }

    static async getUserInterestsWithDetails(userId) {
        try {
            const details = await UserInterest.getUserInterestsWithDetails(userId);
            if (!details) {
                return null;
            }
            
            return {
                ...details,
                categories: typeof details.categories === 'string' 
                    ? JSON.parse(details.categories) 
                    : details.categories
            };
        } catch (error) {
            throw new AppError('Error fetching user interests with details', 500);
        }
    }

    static async updateUserInterests(userId, categories) {
        try {
            // Validate categories array
            if (!Array.isArray(categories)) {
                throw new AppError('Categories must be an array', 400);
            }

            const interests = await UserInterest.upsert(userId, categories);
            
            return {
                id: interests.id,
                userId: interests.userId,
                categories: interests.getParsedCategories(),
                createdAt: interests.createdAt,
                updatedAt: interests.updatedAt
            };
        } catch (error) {
            if (error instanceof AppError) {
                throw error;
            }
            throw new AppError('Error updating user interests', 500);
        }
    }

    static async createUserInterests(userId, categories) {
        try {
            // Check if interests already exist
            const existing = await UserInterest.findByUserId(userId);
            if (existing) {
                throw new AppError('User interests already exist', 400);
            }

            // Validate categories array
            if (!Array.isArray(categories)) {
                throw new AppError('Categories must be an array', 400);
            }

            const interests = await UserInterest.create({ userId, categories });
            
            return {
                id: interests.id,
                userId: interests.userId,
                categories: interests.getParsedCategories(),
                createdAt: interests.createdAt,
                updatedAt: interests.updatedAt
            };
        } catch (error) {
            if (error instanceof AppError) {
                throw error;
            }
            throw new AppError('Error creating user interests', 500);
        }
    }

    static async deleteUserInterests(userId) {
        try {
            const interests = await UserInterest.findByUserId(userId);
            if (!interests) {
                throw new AppError('User interests not found', 404);
            }

            const deleted = await UserInterest.delete(userId);
            if (!deleted) {
                throw new AppError('Failed to delete user interests', 500);
            }

            return { message: 'User interests deleted successfully' };
        } catch (error) {
            if (error instanceof AppError) {
                throw error;
            }
            throw new AppError('Error deleting user interests', 500);
        }
    }

    static async addCategoryToUserInterests(userId, categoryId) {
        try {
            const interests = await this.getUserInterests(userId);
            const categories = interests.categories || [];
            
            // Check if category already exists
            if (categories.includes(categoryId)) {
                throw new AppError('Category already in user interests', 400);
            }
            
            categories.push(categoryId);
            return await this.updateUserInterests(userId, categories);
        } catch (error) {
            if (error instanceof AppError) {
                throw error;
            }
            throw new AppError('Error adding category to user interests', 500);
        }
    }

    static async removeCategoryFromUserInterests(userId, categoryId) {
        try {
            const interests = await this.getUserInterests(userId);
            const categories = interests.categories || [];
            
            // Filter out the category
            const updatedCategories = categories.filter(cat => cat !== categoryId);
            
            if (updatedCategories.length === categories.length) {
                throw new AppError('Category not found in user interests', 404);
            }
            
            return await this.updateUserInterests(userId, updatedCategories);
        } catch (error) {
            if (error instanceof AppError) {
                throw error;
            }
            throw new AppError('Error removing category from user interests', 500);
        }
    }
}

module.exports = UserInterestsService;
