const { Server } = require('socket.io');
const jwt = require('jsonwebtoken');
const { pool } = require('../config/config');
const leaderBoardModel = require('../models/leaderboardModel');
const ChallengeUserMappingModel = require('../models/challengeUserMappingModel');
const SocketManager = require('../config/socket');

// Mock dependencies
jest.mock('socket.io');
jest.mock('jsonwebtoken');
jest.mock('../config/config');
jest.mock('../models/leaderboardModel');
jest.mock('../models/challengeUserMappingModel');

describe('SocketManager', () => {
  let socketManager;
  let mockServer;
  let mockSocket;
  let mockIo;

  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();

    // Use fake timers
    jest.useFakeTimers();

    // Mock socket.io Server
    mockIo = {
      use: jest.fn(),
      on: jest.fn(),
      to: jest.fn().mockReturnThis(),
      emit: jest.fn(),
      sockets: {
        adapter: {
          rooms: new Map()
        }
      }
    };
    Server.mockImplementation(() => mockIo);

    // Mock socket
    mockSocket = {
      id: 'socket-id-123',
      join: jest.fn(),
      on: jest.fn(),
      emit: jest.fn(),
      onAny: jest.fn()
    };

    // Mock server
    mockServer = {};

    // Create SocketManager instance
    socketManager = new SocketManager(mockServer);
  });

  afterEach(() => {
    // Clean up any intervals that might have been created
    if (socketManager) {
      // Call cleanup method to clear all intervals
      if (typeof socketManager.cleanup === 'function') {
        socketManager.cleanup();
      }

      // Clear all intervals manually as a fallback
      for (const [key, intervalId] of socketManager.updateIntervals.entries()) {
        clearInterval(intervalId);
      }
      socketManager.updateIntervals.clear();

      for (const [key, intervalId] of socketManager.leaderboardUpdateIntervals.entries()) {
        clearInterval(intervalId);
      }
      socketManager.leaderboardUpdateIntervals.clear();
    }

    // Restore real timers
    jest.useRealTimers();
  });

  describe('joinGame handler', () => {
    test('should update ranks and emit initial leaderboard data on join', async () => {
      // Arrange
      const token = 'valid-token';
      const challengeId = 123;
      const userId = 456;
      const gameKey = `challenge-${challengeId}`;
      const userGameKey = `${userId}-${challengeId}`;

      // Mock JWT verification
      jwt.verify.mockReturnValue({ id: userId });

      // Mock database query results
      pool.query.mockResolvedValueOnce([[]]);  // No existing game state

      // Mock updateRanks method
      ChallengeUserMappingModel.updateRanks.mockResolvedValue([
        { id: 1, userId: 456, score: 100, rank: 1 },
        { id: 2, userId: 789, score: 50, rank: 2 }
      ]);

      // Mock leaderboard data
      const mockLeaderboardData = [
        { rank: 1, userId: 456, fullName: 'User 1', score: 100 },
        { rank: 2, userId: 789, fullName: 'User 2', score: 50 }
      ];
      leaderBoardModel.getChallengeLeaderboardData.mockResolvedValue(mockLeaderboardData);

      // Simulate the 'joinGame' event
      const joinGameHandler = mockIo.on.mock.calls[0][1]; // Get the connection handler
      await joinGameHandler(mockSocket); // Call the handler with mock socket

      // Get the joinGame event handler
      const eventHandlers = mockSocket.on.mock.calls;
      const joinGameEventHandler = eventHandlers.find(call => call[0] === 'joinGame')[1];

      // Act
      await joinGameEventHandler({ token, challengeId });

      // Assert
      expect(ChallengeUserMappingModel.updateRanks).toHaveBeenCalledWith(challengeId);
      expect(leaderBoardModel.getChallengeLeaderboardData).toHaveBeenCalledWith(challengeId, 10);
      expect(mockSocket.emit).toHaveBeenCalledWith('gameleaderboardData', { leaderboardData: mockLeaderboardData });
      expect(mockSocket.join).toHaveBeenCalledWith(gameKey);
    });

    test('should include rank in gameState when emitting to client', async () => {
      // Arrange
      const token = 'valid-token';
      const challengeId = 123;
      const userId = 456;
      const gameKey = `challenge-${challengeId}`;
      const userGameKey = `${userId}-${challengeId}`;

      // Mock JWT verification
      jwt.verify.mockReturnValue({ id: userId });

      // Mock database query results with game state including rank
      const mockGameState = {
        score: 100,
        life: 3,
        level: 2,
        max_life: 5,
        rank: 1
      };
      pool.query.mockResolvedValueOnce([[mockGameState]]);  // Existing game state

      // Simulate the 'joinGame' event
      const joinGameHandler = mockIo.on.mock.calls[0][1]; // Get the connection handler
      await joinGameHandler(mockSocket); // Call the handler with mock socket

      // Get the joinGame event handler
      const eventHandlers = mockSocket.on.mock.calls;
      const joinGameEventHandler = eventHandlers.find(call => call[0] === 'joinGame')[1];

      // Act
      await joinGameEventHandler({ token, challengeId });

      // Assert
      expect(mockSocket.emit).toHaveBeenCalledWith('gameState', {
        score: mockGameState.score,
        life: mockGameState.life,
        level: mockGameState.level,
        maxLife: mockGameState.max_life,
        rank: mockGameState.rank
      });
    });
  });

  describe('leaderboard handler', () => {
    test('should update ranks and emit leaderboard data on manual refresh', async () => {
      // Arrange
      const token = 'valid-token';
      const challengeId = 123;

      // Mock JWT verification
      jwt.verify.mockReturnValue({ id: 456 });

      // Mock updateRanks method
      ChallengeUserMappingModel.updateRanks.mockResolvedValue([
        { id: 1, userId: 456, score: 100, rank: 1 },
        { id: 2, userId: 789, score: 50, rank: 2 }
      ]);

      // Mock leaderboard data
      const mockLeaderboardData = [
        { rank: 1, userId: 456, fullName: 'User 1', score: 100 },
        { rank: 2, userId: 789, fullName: 'User 2', score: 50 }
      ];
      leaderBoardModel.getChallengeLeaderboardData.mockResolvedValue(mockLeaderboardData);

      // Simulate the 'connection' event
      const connectionHandler = mockIo.on.mock.calls[0][1]; // Get the connection handler
      await connectionHandler(mockSocket); // Call the handler with mock socket

      // Get the leaderboard event handler
      const eventHandlers = mockSocket.on.mock.calls;
      const leaderboardEventHandler = eventHandlers.find(call => call[0] === 'leaderboard')[1];

      // Act
      await leaderboardEventHandler({ token, challengeId });

      // Assert
      expect(ChallengeUserMappingModel.updateRanks).toHaveBeenCalledWith(challengeId);
      expect(leaderBoardModel.getChallengeLeaderboardData).toHaveBeenCalledWith(challengeId, 10);
      expect(mockSocket.emit).toHaveBeenCalledWith('gameleaderboardData', { leaderboardData: mockLeaderboardData });
    });
  });

  describe('startPeriodicLeaderboardUpdates', () => {
    test('should update ranks and emit leaderboard data periodically', async () => {
      // Arrange
      const roomKey = 'challenge-123';
      const challengeId = 123;

      // Save the original method
      const originalMethod = socketManager.startPeriodicLeaderboardUpdates;

      // Mock the method to directly call the functions we want to test
      socketManager.startPeriodicLeaderboardUpdates = jest.fn().mockImplementation(async (roomKey, challengeId) => {
        // Call the real updateRanks and getChallengeLeaderboardData methods
        await ChallengeUserMappingModel.updateRanks(challengeId);
        const leaderboardData = await leaderBoardModel.getChallengeLeaderboardData(challengeId, 10);
        mockIo.to(roomKey).emit('gameleaderboardData', { leaderboardData });
        return 999; // Return a fake interval ID
      });

      // Mock the updateRanks method
      ChallengeUserMappingModel.updateRanks.mockResolvedValue([
        { id: 1, userId: 456, score: 100, rank: 1 },
        { id: 2, userId: 789, score: 50, rank: 2 }
      ]);

      // Mock leaderboard data
      const mockLeaderboardData = [
        { rank: 1, userId: 456, fullName: 'User 1', score: 100 },
        { rank: 2, userId: 789, fullName: 'User 2', score: 50 }
      ];
      leaderBoardModel.getChallengeLeaderboardData.mockResolvedValue(mockLeaderboardData);

      // Act
      await socketManager.startPeriodicLeaderboardUpdates(roomKey, challengeId);

      // Assert
      expect(socketManager.startPeriodicLeaderboardUpdates).toHaveBeenCalledWith(roomKey, challengeId);
      expect(ChallengeUserMappingModel.updateRanks).toHaveBeenCalledWith(challengeId);
      expect(leaderBoardModel.getChallengeLeaderboardData).toHaveBeenCalledWith(challengeId, 10);
      expect(mockIo.to).toHaveBeenCalledWith(roomKey);
      expect(mockIo.emit).toHaveBeenCalledWith('gameleaderboardData', { leaderboardData: mockLeaderboardData });

      // Restore the original method
      socketManager.startPeriodicLeaderboardUpdates = originalMethod;
    });
  });

  describe('startPeriodicUpdates', () => {
    test('should include rank in gameState when emitting updates', async () => {
      // Arrange
      const userId = 456;
      const challengeId = 123;
      const userGameKey = `${userId}-${challengeId}`;

      // Mock game state with rank
      const mockGameState = {
        score: 100,
        life: 3,
        level: 2,
        max_life: 5,
        rank: 1
      };

      // Set up active game
      socketManager.activeGames.set(userGameKey, {
        socket: mockSocket,
        userId,
        challengeId,
        gameState: null // Initial state is null
      });

      // Save the original method
      const originalMethod = socketManager.startPeriodicUpdates;

      // Create a synchronous mock implementation (no setInterval)
      socketManager.startPeriodicUpdates = jest.fn().mockImplementation((userGameKey) => {
        const game = socketManager.activeGames.get(userGameKey);
        if (game) {
          // Directly update state and emit - no async/interval
          game.gameState = mockGameState;
          game.socket.emit('gameState', {
            score: mockGameState.score,
            life: mockGameState.life,
            level: mockGameState.level,
            maxLife: mockGameState.max_life,
            rank: mockGameState.rank
          });
        }
        return 999; // Return a fake interval ID
      });

      // Act - no await needed since we're using a synchronous mock
      const result = socketManager.startPeriodicUpdates(userGameKey);

      // Assert
      expect(socketManager.startPeriodicUpdates).toHaveBeenCalledWith(userGameKey);
      expect(mockSocket.emit).toHaveBeenCalledWith('gameState', {
        score: mockGameState.score,
        life: mockGameState.life,
        level: mockGameState.level,
        maxLife: mockGameState.max_life,
        rank: mockGameState.rank
      });

      // Restore the original method
      socketManager.startPeriodicUpdates = originalMethod;
    });
  });

  describe('updateProgress and rank changes', () => {
    test('should update rank when user score changes', async () => {
      // This test verifies the integration between ChallengeUserMappingModel.updateProgress
      // and the rank update functionality

      // Arrange
      const userId = 456;
      const challengeId = 123;
      const scoreIncrease = 50;

      // Mock the updateProgress method to simulate a score change
      const mockUpdatedProgress = {
        level: 2,
        score: 150,
        life: 3,
        status: 1,
        rank: 1 // Updated rank
      };

      ChallengeUserMappingModel.updateProgress = jest.fn().mockResolvedValue(mockUpdatedProgress);

      // Create a mock socket connection and join game
      const token = 'valid-token';
      jwt.verify.mockReturnValue({ id: userId });

      // Mock initial game state
      const initialGameState = {
        score: 100,
        life: 3,
        level: 2,
        max_life: 5,
        rank: 2 // Initial rank
      };

      pool.query.mockResolvedValueOnce([[initialGameState]]);

      // Simulate the 'connection' event
      const connectionHandler = mockIo.on.mock.calls[0][1];
      await connectionHandler(mockSocket);

      // Get the joinGame event handler
      const eventHandlers = mockSocket.on.mock.calls;
      const joinGameEventHandler = eventHandlers.find(call => call[0] === 'joinGame')[1];

      // Join the game
      await joinGameEventHandler({ token, challengeId });

      // Act - simulate a score update through the model
      const result = await ChallengeUserMappingModel.updateProgress(userId, challengeId, scoreIncrease);

      // Assert
      expect(result.rank).toBe(1); // Rank should be updated to 1
      expect(ChallengeUserMappingModel.updateRanks).toHaveBeenCalledWith(challengeId);
    });
  });
});
