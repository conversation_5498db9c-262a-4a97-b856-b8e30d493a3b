// services/leaderboardService.js
const { pool } = require('../config/config'); // Adjust path

/**
 * Fetches the leaderboard data for a specific challenge.
 * @param {number} challengeId - The ID of the challenge.
 * @param {number} limit - The maximum number of users to return. Default 10.
 * @returns {Promise<Array<object>>} - Array of users with rank, fullName, score, avatar.
 */
const  getChallengeLeaderboardData = async (challengeId, limit = 10) =>{

    // Ensure pool is available
    if (!pool) {
        console.error("Database pool is not available in leaderboardService.");
        throw new Error("Database pool not initialized");
    }

    //  // Validate challengeId
    // if (typeof challengeId !== 'number' || !Number.isInteger(challengeId) || challengeId <= 0) {
    //     console.error("Invalid challengeId provided:", challengeId);
    //     throw new Error("Invalid Challenge ID");
    // }
     // Validate limit
    if (typeof limit !== 'number' || isNaN(limit) || !Number.isInteger(limit) || limit <= 0) {
        console.warn("Invalid limit provided, defaulting to 10:", limit);
        limit = 10;
    }


    // SQL to get scores for a specific challenge, ordered
    const sql = `
        SELECT
            u.id AS userId,
            u.fullName,
            u.avatar,
            u.profile_image_path,
            u.isProfileImageAvailable,
            cum.score,
            cum.rank
        FROM tbl_challenge_user_mapping AS cum
        JOIN tbl_users AS u ON cum.userId = u.id
        WHERE cum.challenge_id = ?  -- Filter by the specific challenge ID
        ORDER BY cum.rank ASC    -- Order by rank (which is already calculated)
        LIMIT ?;
    `;

    try {
        const [rows] = await pool.query(sql, [challengeId, limit]);

        // Map results and use the rank from the database
        const rankedLeaderboard = rows.map((entry) => ({
            rank: entry.rank || 0, // Use the rank from the database, default to 0 if not set
            userId: entry.userId,
            fullName: entry.fullName,
            isProfileImageAvailable: entry.isProfileImageAvailable,
            avatar: entry.isProfileImageAvailable ? entry.profile_image_path : entry.avatar || null, // Prefer profile path
            score: Number(entry.score || 0) // Score for this specific challenge
        }));

        return rankedLeaderboard;

    } catch (error) {
        console.error(`Error fetching leaderboard data for challenge ${challengeId}:`, error);
        throw error; // Re-throw
    }
}

// Keep the global one if you might need it elsewhere, otherwise remove it
// async function getLeaderboardData(limit = 10) { ... }

module.exports = {
    // getLeaderboardData, // Export global if needed
    getChallengeLeaderboardData // Export the per-challenge function
};