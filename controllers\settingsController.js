const  settingsService  = require('../services/settingsService');


const getQuestionCategory = async (req, res, next) => {
    try {
        const settings = await settingsService.getQuestionCategory();
        res.success(settings, 'Settings retrieved successfully');
    } catch (error) {
        next(error);
    }
};

const getChallengePeriod = async (req, res, next) => {
    try {
        const settings = await settingsService.getChallengePeriod();
        res.success(settings, 'Settings retrieved successfully');
    } catch (error) {
        next(error);
    }
};


module.exports = {
    getQuestionCategory,
    getChallengePeriod
};