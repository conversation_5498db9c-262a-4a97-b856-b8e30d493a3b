const { pool } = require('../config/config');

class UserInterest {
    constructor(interest) {
        this.id = interest.id;
        this.userId = interest.userId;
        this.categories = interest.categories;
        this.createdAt = interest.createdAt;
        this.updatedAt = interest.updatedAt;
    }

    static async findByUserId(userId) {
        const [rows] = await pool.query('SELECT * FROM tbl_user_interests WHERE userId = ?', [userId]);
        return rows[0] ? new UserInterest(rows[0]) : null;
    }

    static async findById(id) {
        const [rows] = await pool.query('SELECT * FROM tbl_user_interests WHERE id = ?', [id]);
        return rows[0] ? new UserInterest(rows[0]) : null;
    }

    static async create(interestData) {
        const categoriesJson = typeof interestData.categories === 'object' 
            ? JSON.stringify(interestData.categories) 
            : interestData.categories;
            
        const [result] = await pool.query(
            'INSERT INTO tbl_user_interests (userId, categories, createdAt, updatedAt) VALUES (?, ?, NOW(), NOW())',
            [interestData.userId, categoriesJson]
        );
        return this.findById(result.insertId);
    }

    static async update(userId, interestData) {
        const categoriesJson = typeof interestData.categories === 'object' 
            ? JSON.stringify(interestData.categories) 
            : interestData.categories;
            
        await pool.query(
            'UPDATE tbl_user_interests SET categories = ?, updatedAt = NOW() WHERE userId = ?',
            [categoriesJson, userId]
        );
        return this.findByUserId(userId);
    }

    static async upsert(userId, categories) {
        const existing = await this.findByUserId(userId);
        if (existing) {
            return await this.update(userId, { categories });
        } else {
            return await this.create({ userId, categories });
        }
    }

    static async delete(userId) {
        const [result] = await pool.query('DELETE FROM tbl_user_interests WHERE userId = ?', [userId]);
        return result.affectedRows > 0;
    }

    static async getUserInterestsWithDetails(userId) {
        const query = `
            SELECT 
                ui.*,
                u.fullName,
                u.phoneNumber
            FROM tbl_user_interests ui
            JOIN tbl_users u ON ui.userId = u.id
            WHERE ui.userId = ?
        `;
        
        const [rows] = await pool.query(query, [userId]);
        return rows[0] || null;
    }

    getParsedCategories() {
        try {
            return typeof this.categories === 'string' ? JSON.parse(this.categories) : this.categories;
        } catch (error) {
            return [];
        }
    }
}

module.exports = UserInterest;
