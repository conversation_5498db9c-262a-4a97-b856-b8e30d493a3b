const { pool } = require('../config/config');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');

class User {
    constructor(user) {
        this.id = user.id;
        this.fullName = user.fullName;
        this.phoneNumber = user.phoneNumber;
        this.password = user.password;
        this.lives = user.lives || 5; // Default 5 lives
        this.lastLifeUpdate = user.lastLifeUpdate;
        this.isProfileImageAvailable = user.isProfileImageAvailable;
        this.avatar = user.avatar;
        this.profile_image_path = user.profile_image_path;
        this.langId = user.langId;
        this.createdAt = user.createdAt;
        this.updatedAt = user.updatedAt;
    }

    static async findByPhone(phoneNumber) {
        const [rows] = await pool.query('SELECT * FROM tbl_users WHERE phoneNumber = ?', [phoneNumber]);
        return rows[0] ? new User(rows[0]) : null;
    }

    static async findById(id) {
        const [rows] = await pool.query('SELECT * FROM tbl_users WHERE id = ?', [id]);
        return rows[0] ? new User(rows[0]) : null;
    }

    static async getUserInterests(userId) {
  try {
    const [rows] = await pool.query('SELECT * FROM tbl_user_interests WHERE userId = ?', [userId]);
    if (rows.length > 0) {
      const userInterests = rows[0];
      if (userInterests.categories && typeof userInterests.categories === 'string') {
        try {
          userInterests.categories = JSON.parse(userInterests.categories);
        } catch (parseError) {
          console.error('Failed to parse user interests categories:', parseError);
          // Decide what to do in case of error, maybe return as is or empty array
          userInterests.categories = [];
        }
      }
      return userInterests;
    }
    return null;
  } catch (error) {
    console.error('Error in getUserInterests:', error);
    throw error;
  }
}

    static async create(userData) {
        const hashedPassword = await bcrypt.hash(userData.password, 12);
        let query = 'INSERT INTO tbl_users (fullName, phoneNumber, password, createdAt';
        let values = [userData.fullName, userData.phoneNumber, hashedPassword, new Date()];
        let placeholders = '?, ?, ?, ?'; // Initial placeholders
    
        if (userData.isProfileImageAvailable === '1') { // Correct comparison to string '1'
            query += ', isProfileImageAvailable, profile_image_path';
            values.push(userData.isProfileImageAvailable, userData.profile_image_path);
            placeholders += ', ?, ?'; // Add placeholders for these two columns
        } else {
            query += ', isProfileImageAvailable, avatar';
            values.push(userData.isProfileImageAvailable, userData.avatar);
            placeholders += ', ?, ?'; // Add placeholders for these two columns
        }
    
        query += ') VALUES (' + placeholders + ')'; // Construct VALUES clause with dynamic placeholders
    
        const [result] = await pool.query(query, values);
        if(userData.categories && userData.categories.length > 0){
           await this.updateUserInterest(result.insertId, userData.categories);
        }
        return this.findById(result.insertId);
    }

    static async updateProfile(userId, userData) {
        let query = 'UPDATE tbl_users SET fullName = ?, updatedAt= Now()';
        let values = [userData.fullName];
        
        if(userData.profile_image_path){
            query += ', isProfileImageAvailable = ?, profile_image_path = ?';
            values.push(userData.isProfileImageAvailable, userData.profile_image_path);
        }else if (userData.avatar) {
            query += ', isProfileImageAvailable = ?, avatar = ?';
            values.push(userData.isProfileImageAvailable, userData.avatar);
        }
       
        query += ' WHERE id = ?';
        values.push(userId);
        if(userData.categories && userData.categories.length > 0) {
           
         await this.updateUserInterest(userId, userData.categories);
        }
        await pool.query(query, values);
        return this.findById(userId);
    }
    

    static async updateUserInterest(userId, categories) {
      
    
        let query = `
            INSERT INTO tbl_user_interests (userId, categories, createdAt, updatedAt) 
            VALUES (?, ?, ?, ?) 
            ON DUPLICATE KEY UPDATE categories = VALUES(categories), updatedAt = VALUES(updatedAt)
        `;
    
        let now = new Date();
        
        // Convert array to JSON string
        let categoriesStr = JSON.stringify(categories);
    
        let values = [userId, categoriesStr, now, now];
    
        await pool.query(query, values);
    }
    
    static async changePassword(userId, newPassword) {
        const hashedPassword = await bcrypt.hash(newPassword, 12);
        await pool.query('UPDATE tbl_users SET password = ? WHERE id = ?', [hashedPassword, userId]);
    }

    async verifyPassword(password) {
        return bcrypt.compare(password, this.password);
    }

    generateToken() {
        return jwt.sign(
            { id: this.id, phoneNumber: this.phoneNumber },
            process.env.JWT_SECRET,
            { expiresIn: '24h' }
        );
    }

    toJSON() {
        const user = { ...this };
        delete user.password;
        return user;
    }

   
    static async updateLives(userId, amount) {
        try {
            // Get current user data
            const [user] = await pool.query('SELECT * FROM tbl_users WHERE id = ?', [userId]);
            if (!user[0]) {
                throw new Error('User not found');
            }

            // Calculate new lives value
            let newLives = user[0].lives + amount;
            
            // Ensure lives don't go below 0
            newLives = Math.max(newLives, 0);
            
            // Update lives
            const query = `
                UPDATE tbl_users 
                SET lives = ?, lastLifeUpdate = NOW(), updatedAt = NOW()
                WHERE id = ?
            `;
            
            await pool.query(query, [newLives, userId]);

            return {
                lives: newLives,
                lastUpdate: new Date()
            };
        } catch (error) {
            throw error;
        }
    }

    static async getUserLives(userId) {
        try {
            const [user] = await pool.query('SELECT lives, lastLifeUpdate FROM tbl_users WHERE id = ?', [userId]);
            if (!user[0]) {
                throw new Error('User not found');
            }

            return {
                lives: user[0].lives,
                lastUpdate: user[0].lastLifeUpdate
            };
        } catch (error) {
            throw error;
        }
    }

    static async regenerateLives(userId) {
        try {
            const [user] = await pool.query('SELECT lives, lastLifeUpdate FROM tbl_users WHERE id = ?', [userId]);
            if (!user[0]) {
                throw new Error('User not found');
            }

            // Check if enough time has passed since last update (e.g., 30 minutes per life)
            const timeSinceLastUpdate = Math.floor((new Date() - new Date(user[0].lastLifeUpdate)) / (1000 * 60));
            const livesToAdd = Math.floor(timeSinceLastUpdate / 30); // 1 life per 30 minutes

            if (livesToAdd > 0 && user[0].lives < 5) {
                // Calculate new lives value
                let newLives = Math.min(user[0].lives + livesToAdd, 5);
                
                // Update lives
                const query = `
                    UPDATE tbl_users 
                    SET lives = ?, lastLifeUpdate = NOW(), updatedAt = NOW()
                    WHERE id = ?
                `;
                
                await pool.query(query, [newLives, userId]);

                return {
                    lives: newLives,
                    lastUpdate: new Date(),
                    regenerated: livesToAdd
                };
            }

            return {
                lives: user[0].lives,
                lastUpdate: user[0].lastLifeUpdate,
                regenerated: 0
            };
        } catch (error) {
            throw error;
        }
    }
}

module.exports = User;
