const Challenge = require('../models/challengeModel');
const ChallengeCategory = require('../models/challengeCategoryModel');
const ChallengeUserMapping = require('../models/challengeUserMappingModel');
const RecentActivity = require('../models/recent_activityModel');
const { AppError } = require('../middleware/errorMiddleware');

class ChallengeService {
    static async getAllChallenges() {
        return await Challenge.findAll();
    }

    static async getChallengeById(id) {
        const challenge = await Challenge.findById(id);
        if (!challenge) {
            throw new AppError('Challenge not found', 404);
        }
        return challenge;
    }

    static async getChallengesByCategory(categoryId) {
        // Verify category exists
        const category = await ChallengeCategory.findById(categoryId);
        if (!category) {
            throw new AppError('Category not found', 404);
        }
        return await Challenge.findByCategoryId(categoryId);
    }

    static async getStartedChallenges(userId, categoryId) {
        if (!userId) {
            throw new AppError('User ID is required', 400);
        }
        const challenges = await ChallengeUserMapping.findUserChallenges(userId, categoryId);
        // console.log(challenges);
       // status 1 for started
        return challenges;  // status 1 for started
    }

    static async getNotStartedChallenges(userId, categoryId) {
        if (!userId) {
            throw new AppError('User ID is required', 400);
        }
        return await ChallengeUserMapping.findNotStartedChallenges(userId,categoryId);
    }

    static async startChallenge(userId, challengeId) {
        // Validate inputs
        if (!userId || !challengeId) {
            throw new AppError('User ID and Challenge ID are required', 400);
        }

        // Check if user has already started this challenge
        const existingMapping = await ChallengeUserMapping.checkChallengeStarted(userId, challengeId);
        if (existingMapping) {
            return existingMapping; // Return existing mapping instead of throwing error
        }

        // // Get challenge details for activity logging
        const challenge = await Challenge.findById(challengeId);
        if (!challenge) {
            throw new AppError('Challenge not found', 404);
        }

        // Start the challenge
        const result = await ChallengeUserMapping.startChallenge(userId, challengeId);

       // Log recent activity for game start

            await RecentActivity.create({
                userId: userId,
                activity: `Joined a Challenge: ${challenge.name}`
            });


        return result;
    }

    static async getJoinedChallenges(userId) {
        if (!userId) {
            throw new AppError('User ID is required', 400);
        }
        const challenges = await ChallengeUserMapping.findUserJoinedChallenges(userId, 1);
       // status 1 for started
        return challenges;  // status 1 for started
    }

    static async createChallenge(challengeData) {
        // Validate challenge data
        if (!challengeData.name || !challengeData.winning_rules  || !challengeData.reward || !challengeData.category_id) {
            throw new AppError('Please provide all required fields', 400);
        }

        // Verify category exists
        const category = await ChallengeCategory.findById(challengeData.category_id);
        if (!category) {
            throw new AppError('Invalid category_id', 400);
        }

        return await Challenge.create(challengeData);
    }

    static async updateChallenge(id, challengeData) {
        // Check if challenge exists
        const challenge = await this.getChallengeById(id);

        // If category_id is being updated, verify it exists
        if (challengeData.category_id) {
            const category = await ChallengeCategory.findById(challengeData.category_id);
            if (!category) {
                throw new AppError('Invalid category_id', 400);
            }
        }

        // Update challenge
        return await Challenge.update(id, {
            name: challengeData.name || challenge.name,
            winning_rules : challengeData.winning_rules  || challenge.winning_rules ,
            reward: challengeData.reward || challenge.reward,
            category_id: challengeData.category_id || challenge.category_id
        });
    }

    static async deleteChallenge(id) {
        // Check if challenge exists
        await this.getChallengeById(id);

        // Delete challenge
        const deleted = await Challenge.delete(id);
        if (!deleted) {
            throw new AppError('Failed to delete challenge', 500);
        }
        return true;
    }

    static async getTopSpecialChallenges(limit = 10,userId) {
        return await Challenge.findTopSpecialChallenges(limit,userId);
    }

    static async searchChallenges(filters, userId) {
        const sanitizedFilters = {
            search: filters.search,
            category_id: filters.category_id ? parseInt(filters.category_id) : null,
            min_winning_rules : filters.min_winning_rules  ? parseInt(filters.min_winning_rules ) : null,
            min_reward: filters.min_reward ? parseInt(filters.min_reward) : null,
            status: filters.status,
            min_user_life: filters.min_user_life ? parseInt(filters.min_user_life) : null,
            created_date: filters.created_date,
            start_date: filters.start_date,
            end_date: filters.end_date,
            participation_status: filters.participation_status,
            sort_by: filters.sort_by,
            limit: filters.limit ? parseInt(filters.limit) : 10,
            page: filters.page ? parseInt(filters.page) : 1
        };

        return await Challenge.searchChallenges(sanitizedFilters, userId);
    }

    

    static async getCompletedChallenges(userId) {
        if (!userId) {
            throw new AppError('User ID is required', 400);
        }
        const completedChallenges = await Challenge.findCompletedChallenges(userId);

        return completedChallenges;
    }

    static async getTopChallenges() {
        return await Challenge.findByStatus(1);
    }

    static async getChallengesByCategoryId(categoryId) {
        return await Challenge.findByCategoryId(categoryId);
    }
}

module.exports = ChallengeService;