const { pool } = require('../config/config');

const getQuestionCategory = async () => {

    try {
        const query = `
            SELECT 
                *
            FROM tbl_questions_categories
        `;

        const [categories] = await pool.query(query);

        return categories;
    } catch (error) {
        console.error('Error in getQuestionCategory:', error);
        throw error;
    }

};

const getChallengePeriod = async () => {

    try {
        const query = `
            SELECT 
                *
            FROM tbl_challenge_periods
        `;

        const [periods] = await pool.query(query);

        return periods;
    } catch (error) {
        console.error('Error in getChallengePeriod:', error);
        throw error;
    }

};

const getLevelSettings = async () => {

    try {
        const query = `
            SELECT 
                *
            FROM tbl_level_settings
        `;

        const [levels] = await pool.query(query);

        return levels;
    } catch (error) {
        console.error('Error in getLevelSettings:', error);
        throw error;
    }

};

module.exports = {
    getQuestionCategory,
    getChallengePeriod,
    getLevelSettings
};