// This file contains setup and teardown code for Jest tests

// Set up fake timers for all tests
beforeEach(() => {
  jest.useFakeTimers();
});

// Clean up any timers after each test
afterEach(() => {
  jest.clearAllTimers();
  jest.useRealTimers();
});

// Clean up any mocks after each test
afterEach(() => {
  jest.clearAllMocks();
  jest.restoreAllMocks();
});

// Global teardown to ensure all timers are cleared
afterAll(() => {
  jest.clearAllTimers();
  jest.useRealTimers();
});
