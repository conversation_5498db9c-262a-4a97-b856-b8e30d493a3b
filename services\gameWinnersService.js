const GameWinner = require('../models/gameWinnersModel');
const { AppError } = require('../middleware/errorMiddleware');

class GameWinnersService {
    static async getAllWinners(limit = 10) {
        try {
            return await GameWinner.getWinnersWithDetails(null, limit);
        } catch (error) {
            throw new AppError('Error fetching game winners', 500);
        }
    }

    static async getWinnersByChallenge(challengeId, limit = 10) {
        try {
            return await GameWinner.getWinnersWithDetails(challengeId, limit);
        } catch (error) {
            throw new AppError('Error fetching winners for challenge', 500);
        }
    }

    static async getWinnersByUser(userId) {
        try {
            return await GameWinner.findByUserId(userId);
        } catch (error) {
            throw new AppError('Error fetching user wins', 500);
        }
    }

    static async createWinner(winnerData) {
        try {
            // Check if winner already exists for this challenge
            const existingWinner = await GameWinner.findByUserAndChallenge(
                winnerData.userId, 
                winnerData.challengeId
            );
            
            if (existingWinner) {
                throw new AppError('Winner already exists for this challenge', 400);
            }

            return await GameWinner.create(winnerData);
        } catch (error) {
            if (error instanceof AppError) {
                throw error;
            }
            throw new AppError('Error creating game winner', 500);
        }
    }

    static async deleteWinner(winnerId) {
        try {
            const winner = await GameWinner.findById(winnerId);
            if (!winner) {
                throw new AppError('Winner not found', 404);
            }

            const deleted = await GameWinner.delete(winnerId);
            if (!deleted) {
                throw new AppError('Failed to delete winner', 500);
            }

            return { message: 'Winner deleted successfully' };
        } catch (error) {
            if (error instanceof AppError) {
                throw error;
            }
            throw new AppError('Error deleting winner', 500);
        }
    }

    static async checkWinnerExists(userId, challengeId) {
        try {
            const winner = await GameWinner.findByUserAndChallenge(userId, challengeId);
            return !!winner;
        } catch (error) {
            throw new AppError('Error checking winner status', 500);
        }
    }
}

module.exports = GameWinnersService;
