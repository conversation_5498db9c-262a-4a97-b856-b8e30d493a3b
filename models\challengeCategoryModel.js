const { pool } = require('../config/config');

class ChallengeCategory {
    constructor(category) {
        this.id = category.id;
        this.name = category.name;
        this.icon = category.icon;
        this.color = category.color;
        this.langId = category.langId;
        this.iconId = category.iconId;
        this.createdAt = category.createdAt;
        this.updatedAt = category.updatedAt;
    }

    static async findAll() {
        const [rows] = await pool.query('SELECT * FROM tbl_challenge_categories ORDER BY createdAt DESC');
        return rows.map(row => new ChallengeCategory(row));
    }

    static async findById(id) {
        const [rows] = await pool.query('SELECT * FROM tbl_challenge_categories WHERE id = ?', [id]);
        return rows[0] ? new ChallengeCategory(rows[0]) : null;
    }

    static async findByLanguage(langId) {
        const [rows] = await pool.query('SELECT * FROM tbl_challenge_categories WHERE langId = ? ORDER BY createdAt DESC', [langId]);
        return rows.map(row => new ChallengeCategory(row));
    }

    static async create(categoryData) {
        const [result] = await pool.query(
            'INSERT INTO tbl_challenge_categories (name, icon, color, langId, iconId, createdAt, updatedAt) VALUES (?, ?, ?, ?, ?, NOW(), NOW())',
            [categoryData.name, categoryData.icon, categoryData.color, categoryData.langId, categoryData.iconId]
        );
        return this.findById(result.insertId);
    }

    static async update(id, categoryData) {
        await pool.query(
            'UPDATE tbl_challenge_categories SET name = ?, icon = ?, color = ?, langId = ?, iconId = ?, updatedAt = NOW() WHERE id = ?',
            [categoryData.name, categoryData.icon, categoryData.color, categoryData.langId, categoryData.iconId, id]
        );
        return this.findById(id);
    }

    static async delete(id) {
        const [result] = await pool.query('DELETE FROM tbl_challenge_categories WHERE id = ?', [id]);
        return result.affectedRows > 0;
    }
}

module.exports = ChallengeCategory; 