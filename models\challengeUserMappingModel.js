const { pool } = require('../config/config');

const AskedQuestion = require('../models/askedQuestionModel');
const DatabaseMigrationService = require('../services/databaseMigrationService');
class ChallengeUserMapping {
    constructor(mapping) {
        this.id = mapping.id;
        this.userId = mapping.userId;
        this.challenge_id = mapping.challenge_id;
        this.status = mapping.status;
        this.level = mapping.level;
        this.score = mapping.score;
        this.life = mapping.life;
        this.createdAt = mapping.createdAt;
        this.updatedAt = mapping.updatedAt;
        this.winning_rules = mapping.winning_rules;
    }

    static async findUserJoinedChallenges(userId, status = null) {
        let query = `
            SELECT c.*, cum.score, cum.status, cum.level,
                   cc.name as category_name, cc.icon as category_icon
            FROM tbl_challenges c
            INNER JOIN tbl_challenge_user_mapping cum ON c.id = cum.challenge_id
            LEFT JOIN tbl_challenge_categories cc ON c.category_id = cc.id
            WHERE cum.userId = ?
        `;

        const params = [userId];
        if (status !== null) {
            query += ' AND cum.status = ?';
            params.push(status);
        }

        query += ' ORDER BY cum.createdAt DESC';

        const [rows] = await pool.query(query, params);
        return rows.map(row => {
            // Parse winning_rules
            this.parseWinningRules(row);

            return {
                ...row
            };
        });
    }
    // Helper method to parse winning_rules JSON string
    static parseWinningRules(row) {
        if (row.winning_rules && typeof row.winning_rules === 'string') {
            try {
                row.winning_rules = JSON.parse(row.winning_rules);
            } catch (error) {
                // If parsing fails, keep the original value
                console.error('Error parsing winning_rules:', error);
            }
        }
        return row;
    }

    static async findUserChallenges(userId, categoryId) {
        const query = `
            SELECT 
                c.id,
                c.name,
                c.winning_rules,
                c.reward,
                c.category_id,
                c.start_date,
                c.end_date,
                c.createdAt,
                c.updatedAt,
                c.user_life,
                c.description,
                c.period_type,
                cum.score, cum.status, cum.level,cum.rank,
                cc.name as category_name, cc.icon as category_icon
            FROM tbl_challenges c
            INNER JOIN tbl_challenge_user_mapping cum ON c.id = cum.challenge_id
            LEFT JOIN tbl_challenge_categories cc ON c.category_id = cc.id
            WHERE cum.userId = ? AND c.category_id = ? AND cum.status IN (1, 2)
            ORDER BY cum.createdAt DESC
        `;
    
        const params = [userId, categoryId];
    
        const [rows] = await pool.query(query, params);
        return rows.map(row => {
            this.parseWinningRules(row);
            return {
                ...row,
                category: row.category_name ? {
                    id: row.category_id,
                    name: row.category_name,
                    icon: row.category_icon
                } : null
            };
        });
    }
    


    static async findNotStartedChallenges(userId, categoryId) {
        // console.log("Fetching not started challenges for userId:", userId, "categoryId:", categoryId); // Added more context

        const query = `
         SELECT
            c.id,
            c.name,
            c.winning_rules,
            c.reward,
            c.category_id,
            c.start_date,
            c.end_date,
            c.createdAt,
            c.updatedAt,
            c.user_life,
            c.description,
            c.period_type,
            cc.name AS category_name,
            cc.icon AS category_icon,
            cum.status  -- This will likely always be NULL because of the NOT EXISTS clause
        FROM tbl_challenges c
        LEFT JOIN tbl_challenge_categories cc
            ON c.category_id = cc.id
        LEFT JOIN tbl_challenge_user_mapping cum
            ON c.id = cum.challenge_id
            AND cum.userId = ?
        WHERE NOT EXISTS (
            SELECT 1 FROM tbl_challenge_user_mapping sub_cum -- Using a different alias is good practice
            WHERE sub_cum.challenge_id = c.id
            AND sub_cum.userId = ?
        )
        AND c.category_id = ? And c.status = 1
        ORDER BY c.createdAt DESC;
        `;

        // Provide userId twice, then categoryId
        const [rows] = await pool.query(query, [userId, userId, categoryId]);

        // Parse winning_rules and map the results
        return rows.map(row => {
            // Parse winning_rules
            this.parseWinningRules(row);

            return {
                ...row,
                // Remove redundant category fields if you are creating the nested 'category' object
                category_name: undefined,
                category_icon: undefined,
                category_id: undefined, // Also remove this if it's only in the nested object
                category: row.category_name ? {
                    id: row.category_id,
                    name: row.category_name,
                    icon: row.category_icon
                } : null
            };
        });
    }

    static async checkChallengeStarted(userId, challengeId) {
        const [rows] = await pool.query(
            'SELECT * FROM tbl_challenge_user_mapping WHERE userId = ? AND challenge_id = ?',
            [userId, challengeId]
        );
        return rows[0] ? new ChallengeUserMapping(rows[0]) : null;
    }

    static async startChallenge(userId, challengeId) {
        try {
            // Check if challenge exists and get user_life
            const [challenge] = await pool.query(
                'SELECT user_life FROM tbl_challenges WHERE id = ?',
                [challengeId]
            );

            if (!challenge[0]) {
                throw new Error('Challenge not found');
            }

            // Get the translation ID for this challenge (needed for foreign key constraint)
            const translationId = await DatabaseMigrationService.getChallengeTranslationId(challengeId);
            if (!translationId) {
                throw new Error('Challenge translation not found. Please ensure translations are initialized.');
            }

            // Check if user already started this challenge (using translation ID)
            const [existingMapping] = await pool.query(
                'SELECT * FROM tbl_challenge_user_mapping WHERE userId = ? AND challenge_id = ?',
                [userId, translationId]
            );

            if (existingMapping[0]) {
                throw new Error('Challenge already started');
            }

            // Start new challenge with initial life from challenge settings
            // Note: challenge_id now references tbl_challenges_translation.id due to FK constraint
            const query = `
                INSERT INTO tbl_challenge_user_mapping
                (userId, challenge_id, status, level, score, life, createdAt, updatedAt)
                VALUES (?, ?, 1, 1, 0, ?, NOW(), NOW())
            `;

            const [result] = await pool.query(query, [userId, translationId, challenge[0].user_life]);

            return new ChallengeUserMapping({
                id: result.insertId,
                userId,
                challenge_id: translationId, // Store the translation ID as that's what's in the database
                status: 1,
                level: 1,
                score: 0,
                life: challenge[0].user_life,
                createdAt: new Date(),
                updatedAt: new Date()
            });
        } catch (error) {
            throw error;
        }
    }

    static async updateProgress(userId, challengeId, scoreIncrease) {
        try {
            // Get current progress
            const [currentProgress] = await pool.query(
                'SELECT * FROM tbl_challenge_user_mapping WHERE userId = ? AND challenge_id = ?',
                [userId, challengeId]
            );

            if (!currentProgress[0]) {
                throw new Error('Challenge progress not found');
            }

            const newScore = currentProgress[0].score + scoreIncrease;
            let newLevel = currentProgress[0].level;

            // Level up logic (every 100 points)
            if (Math.floor(newScore / 100) > Math.floor(currentProgress[0].score / 100)) {
                newLevel++;
            }

            // Update progress
            const query = `
                UPDATE tbl_challenge_user_mapping
                SET score = ?, level = ?, updatedAt = NOW()
                WHERE userId = ? AND challenge_id = ?
            `;

            await pool.query(query, [newScore, newLevel, userId, challengeId]);

            // Update ranks for all users in this challenge
            await this.updateRanks(challengeId);
            // console.log(`ChallengeUserMapping: Updated ranks for challenge ${challengeId} after score update`);

            // Get the updated rank for this user
            const [updatedUserData] = await pool.query(
                'SELECT rank FROM tbl_challenge_user_mapping WHERE userId = ? AND challenge_id = ?',
                [userId, challengeId]
            );

            return {
                level: newLevel,
                score: newScore,
                life: currentProgress[0].life,
                status: currentProgress[0].status,
                rank: updatedUserData[0]?.rank || 0
            };
        } catch (error) {
            throw error;
        }
    }

    static async getCurrentProgress(userId, challengeId) {

        try {
            const query = `
                SELECT m.*
                FROM tbl_challenge_user_mapping m
                JOIN tbl_challenges c ON m.challenge_id = c.id
                WHERE m.userId = ? AND m.challenge_id = ?
            `;

            const [rows] = await pool.query(query, [userId, challengeId]);
            return rows[0];
        } catch (error) {
            throw error;
        }
    }

    static async updateLife(userId, challengeId,qId, decrease = true) {
        try {
            // Get current progress and challenge info
            const [rows] = await pool.query(`
                SELECT m.*, c.user_life
                FROM tbl_challenge_user_mapping m
                JOIN tbl_challenges c ON m.challenge_id = c.id
                WHERE m.userId = ? AND m.challenge_id = ?
            `, [userId, challengeId]);

            const currentProgress = rows[0];
            if (!currentProgress) {
                throw new Error('Challenge progress not found');
            }


            // Calculate new life value
            let newLife = decrease ? currentProgress.life - 1 : currentProgress.life + 1;

            // Ensure life doesn't exceed maximum
            newLife = Math.min(newLife, currentProgress.user_life);

            // Ensure life doesn't go below 0
            newLife = Math.max(newLife, 0);
            await AskedQuestion.markQuestionAsAsked(userId, qId);


            // Update life
            const query = `
                UPDATE tbl_challenge_user_mapping
                SET life = ?, updatedAt = NOW()
                WHERE userId = ? AND challenge_id = ?
            `;

            await pool.query(query, [newLife, userId, challengeId]);

            return {
                life: newLife,
                maxLife: currentProgress.user_life,
                level: currentProgress.level,
                score: currentProgress.score,
                status: currentProgress.status
            };
        } catch (error) {
            throw error;
        }
    }

    static async getUserChallenges(userId) {
        try {
            const query = `
                SELECT
                    m.*,
                    c.name as challenge_name,
                    c.description as challenge_description,
                    c.user_life as max_life
                FROM tbl_challenge_user_mapping m
                JOIN tbl_challenges c ON m.challenge_id = c.id
                WHERE m.userId = ?
            `;

            const [rows] = await pool.query(query, [userId]);
            return rows;
        } catch (error) {
            throw error;
        }
    }

    static async getMappingsByChallengeId (challengeId) {
        const [rows] = await pool.query(`SELECT * FROM tbl_challenge_user_mapping WHERE challenge_id = ?`, [challengeId]);
        return rows[0];
      }

      static async refillLife(id, life){
        const [result] = await pool.query(`UPDATE tbl_challenge_user_mapping SET life = ?, updatedAt = NOW() WHERE id = ?`, [life, id]);
        return result;
      }

      /**
       * Ends a challenge by updating the status in both tbl_challenges and tbl_challenge_user_mapping tables.
       *
       * @param {number} challengeId - The ID of the challenge to end.
       * @param {number} userId - The ID of the user whose challenge is being ended.
       * @returns {Promise<Object>} - Object containing the result of the operation.
       */
      /**
       * Checks if a user is a winner based on the challenge's winning rules
       *
       * @param {number} challengeId - The ID of the challenge
       * @param {number} userId - The ID of the user
       * @returns {Promise<Object>} - Object containing whether the user is a winner and the winning rules
       */
      static async checkWinner(challengeId, userId) {
        try {
            // console.log(`Checking if user ID: ${userId} is a winner for challenge ID: ${challengeId}`);

            // Get the challenge details including winning_rules
            const [challengeResult] = await pool.query(`
                SELECT * FROM tbl_challenges
                WHERE id = ?
            `, [challengeId]);

            if (!challengeResult || challengeResult.length === 0) {
                throw new Error(`Challenge with ID ${challengeId} not found`);
            }

            const challenge = challengeResult[0];

            // Get the user's current progress
            const [userProgressResult] = await pool.query(`
                SELECT * FROM tbl_challenge_user_mapping
                WHERE challenge_id = ? AND userId = ?
            `, [challengeId, userId]);

            if (!userProgressResult || userProgressResult.length === 0) {
                throw new Error(`User progress for user ID ${userId} in challenge ID ${challengeId} not found`);
            }

            const userProgress = userProgressResult[0];

            // Parse winning_rules if it's a string
            let winningRules = challenge.winning_rules;
            if (typeof winningRules === 'string') {
                try {
                    winningRules = JSON.parse(winningRules);
                } catch (error) {
                    console.error(`Error parsing winning_rules for challenge ID ${challengeId}:`, error);
                    throw new Error(`Invalid winning_rules format for challenge ID ${challengeId}`);
                }
            }

            // console.log(`Winning rules for challenge ID ${challengeId}:`, winningRules);
            // console.log(`User progress for user ID ${userId}:`, userProgress);

            // Check if the user meets all winning criteria
            const isWinner = (
                (!winningRules.level || userProgress.level >= winningRules.level) &&
                (!winningRules.winning_points || userProgress.score >= winningRules.winning_points) &&
                (!winningRules.rank || userProgress.rank <= winningRules.rank)
            );

            // console.log(`User ID ${userId} is ${isWinner ? 'a winner' : 'not a winner'} for challenge ID ${challengeId}`);

            return {
                isWinner,
                winningRules
            };
        } catch (error) {
            console.error(`Error checking winner status for user ID ${userId} in challenge ID ${challengeId}:`, error);
            throw error;
        }
      }

      /**
       * Adds a user to the winners table
       *
       * @param {number} challengeId - The ID of the challenge
       * @param {number} userId - The ID of the user
       * @returns {Promise<Object>} - Object containing the result of the operation
       */
      static async addWinner(challengeId, userId) {
        try {
            // console.log(`Adding user ID: ${userId} as winner for challenge ID: ${challengeId}`);

            // Check if the user is already in the winners table for this challenge
            const [existingWinner] = await pool.query(`
                SELECT * FROM tbl_game_winners
                WHERE challengeId = ? AND userId = ?
            `, [challengeId, userId]);

            if (existingWinner && existingWinner.length > 0) {
                // console.log(`User ID ${userId} is already a winner for challenge ID ${challengeId}`);
                return {
                    success: true,
                    message: 'User is already a winner',
                    isNewWinner: false
                };
            }

            // Add the user to the winners table
            const [result] = await pool.query(`
                INSERT INTO tbl_game_winners (userId, challengeId, createdAt, updatedAt)
                VALUES (?, ?, NOW(), NOW())
            `, [userId, challengeId]);

            // console.log(`Successfully added user ID ${userId} as winner for challenge ID ${challengeId}`);

            return {
                success: true,
                message: 'User added as winner successfully',
                isNewWinner: true,
                winnerId: result.insertId
            };
        } catch (error) {
            console.error(`Error adding user ID ${userId} as winner for challenge ID ${challengeId}:`, error);
            throw error;
        }
      }

      static async endChallenge(challengeId, userId) {
        try {
            // console.log(`Ending challenge ID: ${challengeId} for user ID: ${userId}`);

            // Use a transaction to ensure all updates are atomic
            const connection = await pool.getConnection();
            try {
                await connection.beginTransaction();

                // Update the challenge status in tbl_challenges to 2 (ended)
                const updateChallengeQuery = `
                    UPDATE tbl_challenges
                    SET status = 2, updatedAt = NOW()
                    WHERE id = ?
                `;
                await connection.query(updateChallengeQuery, [challengeId]);

                // Update the user's challenge mapping status in tbl_challenge_user_mapping to 2 (ended)
                const updateMappingQuery = `
                    UPDATE tbl_challenge_user_mapping
                    SET status = 2, updatedAt = NOW()
                    WHERE challenge_id = ? AND userId = ?
                `;
                await connection.query(updateMappingQuery, [challengeId, userId]);

                await connection.commit();
                // console.log(`Successfully ended challenge ID: ${challengeId} for user ID: ${userId}`);

                // Update ranks for all users in this challenge
                await this.updateRanks(challengeId);
                // console.log(`Updated ranks for challenge ${challengeId} after ending the challenge`);

                // Get the final state to return (including the updated rank)
                const [finalState] = await pool.query(`
                    SELECT m.*, c.user_life as max_life, c.winning_rules
                    FROM tbl_challenge_user_mapping m
                    JOIN tbl_challenges c ON m.challenge_id = c.id
                    WHERE m.userId = ? AND m.challenge_id = ?
                `, [userId, challengeId]);

                // Check if the user is a winner
                const winnerResult = await this.checkWinner(challengeId, userId);

                // If the user is a winner, add them to the winners table
                let winnerAddResult = null;
                if (winnerResult.isWinner) {
                    winnerAddResult = await this.addWinner(challengeId, userId);
                }

                return {
                    success: true,
                    message: 'Challenge ended successfully',
                    finalState: finalState[0] || null,
                    isWinner: winnerResult.isWinner,
                    winningRules: winnerResult.winningRules,
                    winnerAddResult
                };
            } catch (error) {
                await connection.rollback();
                console.error(`Transaction error ending challenge ID: ${challengeId} for user ID: ${userId}:`, error);
                throw error;
            } finally {
                connection.release();
            }
        } catch (error) {
            console.error(`Error ending challenge ID: ${challengeId} for user ID: ${userId}:`, error);
            throw error;
        }
      }

      /**
       * Updates the rank field for all users in a specific challenge based on their scores.
       * Ranks are assigned in descending order of scores (highest score gets rank 1).
       * If multiple users have the same score, they are ranked by the most recent update time.
       *
       * @param {number} challengeId - The ID of the challenge to update ranks for.
       * @returns {Promise<Array>} - Array of updated user mappings with their new ranks.
       */
      static async updateRanks(challengeId) {
        try {
            // console.log(`Updating ranks for challenge ID: ${challengeId}`);

            // First, get all users in this challenge ordered by score (descending)
            // If scores are equal, use the most recent update time as a tiebreaker
            const query = `
                SELECT id, userId, score, updatedAt, rank
                FROM tbl_challenge_user_mapping
                WHERE challenge_id = ?
                ORDER BY score DESC, updatedAt ASC
            `;

            const [users] = await pool.query(query, [challengeId]);

            if (!users || users.length === 0) {
                // console.log(`No users found for challenge ID: ${challengeId}`);
                return [];
            }

            // console.log(`Found ${users.length} users to update ranks for challenge ID: ${challengeId}`);

            // Update each user's rank based on their position in the sorted list
            // Use a transaction to ensure all updates are atomic
            const connection = await pool.getConnection();
            try {
                await connection.beginTransaction();

                const updatePromises = users.map(async (user, index) => {
                    const newRank = index + 1; // Rank starts at 1
                    const oldRank = user.rank || 0;

                    // Only update if rank has changed to reduce unnecessary database operations
                    if (newRank !== oldRank) {
                        const updateQuery = `
                            UPDATE tbl_challenge_user_mapping
                            SET rank = ?, updatedAt = NOW()
                            WHERE id = ?
                        `;

                        await connection.query(updateQuery, [newRank, user.id]);
                        // console.log(`Rank changed for user ID ${user.userId}: ${oldRank} -> ${newRank}`);
                    }

                    return {
                        id: user.id,
                        userId: user.userId,
                        score: user.score,
                        rank: newRank,
                        rankChanged: newRank !== oldRank
                    };
                });

                // Wait for all updates to complete
                const results = await Promise.all(updatePromises);

                await connection.commit();

                // Count how many ranks actually changed
                const changedRanks = results.filter(r => r.rankChanged).length;
                // console.log(`Successfully updated ranks for ${changedRanks} out of ${results.length} users in challenge ID: ${challengeId}`);

                return results;
            } catch (error) {
                await connection.rollback();
                console.error(`Transaction error updating ranks for challenge ID: ${challengeId}:`, error);
                throw error;
            } finally {
                connection.release();
            }
        } catch (error) {
            console.error(`Error updating ranks for challenge ID: ${challengeId}:`, error);
            throw error;
        }
      }
}

module.exports = ChallengeUserMapping;