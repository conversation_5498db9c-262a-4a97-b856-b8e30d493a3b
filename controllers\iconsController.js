const IconsService = require('../services/iconsService');
const { AppError } = require('../middleware/errorMiddleware');

class IconsController {
    // Categories Icons
    static async getAllCategoriesIcons(req, res, next) {
        try {
            const icons = await IconsService.getAllCategoriesIcons();
            res.success(icons, 'Categories icons retrieved successfully');
        } catch (error) {
            next(error);
        }
    }

    static async getCategoriesIconById(req, res, next) {
        try {
            const { id } = req.params;
            const icon = await IconsService.getCategoriesIconById(id);
            res.success(icon, 'Categories icon retrieved successfully');
        } catch (error) {
            next(error);
        }
    }

    static async createCategoriesIcon(req, res, next) {
        try {
            const { icon } = req.body;

            if (!icon) {
                throw new AppError('icon is required', 400);
            }

            const newIcon = await IconsService.createCategoriesIcon({ icon });
            res.success(newIcon, 'Categories icon created successfully');
        } catch (error) {
            next(error);
        }
    }

    static async updateCategoriesIcon(req, res, next) {
        try {
            const { id } = req.params;
            const { icon } = req.body;

            if (!icon) {
                throw new AppError('icon is required', 400);
            }

            const updatedIcon = await IconsService.updateCategoriesIcon(id, { icon });
            res.success(updatedIcon, 'Categories icon updated successfully');
        } catch (error) {
            next(error);
        }
    }

    static async deleteCategoriesIcon(req, res, next) {
        try {
            const { id } = req.params;
            const result = await IconsService.deleteCategoriesIcon(id);
            res.success(result, 'Categories icon deleted successfully');
        } catch (error) {
            next(error);
        }
    }

    // Challenge Categories Icons
    static async getAllChallengeCategoriesIcons(req, res, next) {
        try {
            const icons = await IconsService.getAllChallengeCategoriesIcons();
            res.success(icons, 'Challenge categories icons retrieved successfully');
        } catch (error) {
            next(error);
        }
    }

    static async getChallengeCategoriesIconById(req, res, next) {
        try {
            const { id } = req.params;
            const icon = await IconsService.getChallengeCategoriesIconById(id);
            res.success(icon, 'Challenge categories icon retrieved successfully');
        } catch (error) {
            next(error);
        }
    }

    static async createChallengeCategoriesIcon(req, res, next) {
        try {
            const { icon, color } = req.body;

            if (!icon || !color) {
                throw new AppError('icon and color are required', 400);
            }

            const newIcon = await IconsService.createChallengeCategoriesIcon({ icon, color });
            res.success(newIcon, 'Challenge categories icon created successfully');
        } catch (error) {
            next(error);
        }
    }

    static async updateChallengeCategoriesIcon(req, res, next) {
        try {
            const { id } = req.params;
            const { icon, color } = req.body;

            const updateData = {};
            if (icon) updateData.icon = icon;
            if (color) updateData.color = color;

            if (Object.keys(updateData).length === 0) {
                throw new AppError('At least one field (icon or color) must be provided', 400);
            }

            const updatedIcon = await IconsService.updateChallengeCategoriesIcon(id, updateData);
            res.success(updatedIcon, 'Challenge categories icon updated successfully');
        } catch (error) {
            next(error);
        }
    }

    static async deleteChallengeCategoriesIcon(req, res, next) {
        try {
            const { id } = req.params;
            const result = await IconsService.deleteChallengeCategoriesIcon(id);
            res.success(result, 'Challenge categories icon deleted successfully');
        } catch (error) {
            next(error);
        }
    }
}

module.exports = IconsController;
