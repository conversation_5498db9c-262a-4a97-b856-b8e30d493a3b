const { pool } = require('../config/config');

class ChallengeTranslation {
    constructor(translation) {
        this.id = translation.id;
        this.name = translation.name;
        this.description = translation.description;
        this.challengeId = translation.challengeId;
        this.langId = translation.langId;
        this.createdAt = translation.createdAt;
        this.updatedAt = translation.updatedAt;
    }

    static async findAll() {
        const [rows] = await pool.query('SELECT * FROM tbl_challenges_translation ORDER BY createdAt DESC');
        return rows.map(row => new ChallengeTranslation(row));
    }

    static async findById(id) {
        const [rows] = await pool.query('SELECT * FROM tbl_challenges_translation WHERE id = ?', [id]);
        return rows[0] ? new ChallengeTranslation(rows[0]) : null;
    }

    static async findByChallengeId(challengeId) {
        const [rows] = await pool.query('SELECT * FROM tbl_challenges_translation WHERE challengeId = ?', [challengeId]);
        return rows.map(row => new ChallengeTranslation(row));
    }

    static async findByChallengeAndLanguage(challengeId, langId) {
        const [rows] = await pool.query('SELECT * FROM tbl_challenges_translation WHERE challengeId = ? AND langId = ?', [challengeId, langId]);
        return rows[0] ? new ChallengeTranslation(rows[0]) : null;
    }

    static async findByLanguage(langId) {
        const [rows] = await pool.query('SELECT * FROM tbl_challenges_translation WHERE langId = ? ORDER BY createdAt DESC', [langId]);
        return rows.map(row => new ChallengeTranslation(row));
    }

    static async create(translationData) {
        const [result] = await pool.query(
            'INSERT INTO tbl_challenges_translation (name, description, challengeId, langId, createdAt, updatedAt) VALUES (?, ?, ?, ?, NOW(), NOW())',
            [translationData.name, translationData.description, translationData.challengeId, translationData.langId]
        );
        return this.findById(result.insertId);
    }

    static async update(id, translationData) {
        await pool.query(
            'UPDATE tbl_challenges_translation SET name = ?, description = ?, challengeId = ?, langId = ?, updatedAt = NOW() WHERE id = ?',
            [translationData.name, translationData.description, translationData.challengeId, translationData.langId, id]
        );
        return this.findById(id);
    }

    static async upsert(challengeId, langId, translationData) {
        const existing = await this.findByChallengeAndLanguage(challengeId, langId);
        if (existing) {
            return await this.update(existing.id, { ...translationData, challengeId, langId });
        } else {
            return await this.create({ ...translationData, challengeId, langId });
        }
    }

    static async delete(id) {
        const [result] = await pool.query('DELETE FROM tbl_challenges_translation WHERE id = ?', [id]);
        return result.affectedRows > 0;
    }

    static async deleteByChallengeId(challengeId) {
        const [result] = await pool.query('DELETE FROM tbl_challenges_translation WHERE challengeId = ?', [challengeId]);
        return result.affectedRows;
    }

    static async getChallengesWithTranslations(langId = null, limit = null) {
        let query = `
            SELECT 
                c.*,
                ct.name as translated_name,
                ct.description as translated_description,
                ct.langId
            FROM tbl_challenges c
            LEFT JOIN tbl_challenges_translation ct ON c.id = ct.challengeId
        `;
        
        let params = [];
        if (langId) {
            query += ' WHERE ct.langId = ? OR ct.langId IS NULL';
            params.push(langId);
        }
        
        query += ' ORDER BY c.createdAt DESC';
        
        if (limit) {
            query += ' LIMIT ?';
            params.push(limit);
        }

        const [rows] = await pool.query(query, params);
        return rows;
    }
}

module.exports = ChallengeTranslation;
