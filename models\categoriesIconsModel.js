const { pool } = require('../config/config');

class CategoriesIcon {
    constructor(icon) {
        this.id = icon.id;
        this.icon = icon.icon;
    }

    static async findAll() {
        const [rows] = await pool.query('SELECT * FROM tbl_categories_icons ORDER BY id ASC');
        return rows.map(row => new CategoriesIcon(row));
    }

    static async findById(id) {
        const [rows] = await pool.query('SELECT * FROM tbl_categories_icons WHERE id = ?', [id]);
        return rows[0] ? new CategoriesIcon(rows[0]) : null;
    }

    static async create(iconData) {
        const [result] = await pool.query(
            'INSERT INTO tbl_categories_icons (icon) VALUES (?)',
            [iconData.icon]
        );
        return this.findById(result.insertId);
    }

    static async update(id, iconData) {
        await pool.query(
            'UPDATE tbl_categories_icons SET icon = ? WHERE id = ?',
            [iconData.icon, id]
        );
        return this.findById(id);
    }

    static async delete(id) {
        const [result] = await pool.query('DELETE FROM tbl_categories_icons WHERE id = ?', [id]);
        return result.affectedRows > 0;
    }
}

module.exports = CategoriesIcon;
