Stack trace:
Frame         Function      Args
0007FFFF9F80  00021006118E (00021028DEE8, 000210272B3E, 000000000000, 0007FFFF8E80) msys-2.0.dll+0x2118E
0007FFFF9F80  0002100469BA (000000000000, 000000000000, 000000000000, 000000000004) msys-2.0.dll+0x69BA
0007FFFF9F80  0002100469F2 (00021028DF99, 0007FFFF9E38, 000000000000, 000000000000) msys-2.0.dll+0x69F2
0007FFFF9F80  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFF9F80  00021006A545 (0007FFFF9F90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0001004F94B7  00021006B9A5 (0007FFFF9F90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFAFEDC0000 ntdll.dll
7FFAFE660000 KERNEL32.DLL
7FFAFC0B0000 KERNELBASE.dll
7FFAF8700000 apphelp.dll
7FFAFE220000 USER32.dll
7FFAFC080000 win32u.dll
7FFAFD420000 GDI32.dll
7FFAFC770000 gdi32full.dll
7FFAFC6C0000 msvcp_win.dll
7FFAFC8B0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFAFD360000 advapi32.dll
7FFAFE500000 msvcrt.dll
7FFAFCB80000 sechost.dll
7FFAFE870000 RPCRT4.dll
7FFAFB540000 CRYPTBASE.DLL
7FFAFBF50000 bcryptPrimitives.dll
7FFAFE830000 IMM32.DLL
