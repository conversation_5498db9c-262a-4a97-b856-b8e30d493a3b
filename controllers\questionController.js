const QuestionService = require('../services/questionService');
const { AppError } = require('../middleware/errorMiddleware');

class QuestionController {
    static async getQuestions(req, res, next) {
        try {
            // console.log(req.params.challengeId);
            const questions = await QuestionService.getQuestionsByLevel(
                req.user.id,
                req.params.challengeId
            );
            res.success(questions, 'Questions retrieved successfully');
        } catch (error) {
            next(error);
        }
    }

    static async getQuestionsByType(req, res, next) {
        try {
            const { type } = req.params;
            const questions = await QuestionService.getQuestionsByType(type);
            res.success(questions, `${type} questions retrieved successfully`);
        } catch (error) {
            next(error);
        }
    }

    static async submitAnswer(req, res, next) {
        try {
            const { questionId, answerId } = req.body;
            
            
            if (!questionId || !answerId) {
                throw new AppError('Question ID and Answer ID are required', 400);
            }

            const result = await QuestionService.submitAnswer(
                req.user.id,
                req.params.challengeId,
                questionId,
                answerId
            );

            const message = result.isCorrect 
                ? `Correct answer! +${result.scoreIncrease} points`
                : 'Incorrect answer';

            res.success(result, message);
        } catch (error) {
            next(error);
        }
    }
}

module.exports = QuestionController; 