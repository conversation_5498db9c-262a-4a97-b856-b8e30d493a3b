const jwt = require('jsonwebtoken');
const User = require('../models/userModel');
const { AppError } = require('../middleware/errorMiddleware');
const settingsService  = require('../services/settingsService');
const userRewardService = require('../services/userRewardService');
class AuthService {
    static async register(userData) {
        // Check if user already exists
        const existingUser = await User.findByPhone(userData.phoneNumber);
        if (existingUser) {
            throw new AppError('Phone number already registered', 400);
        }

        // Create new user
        const user = await User.create(userData);
        
        // Generate token
        const token = this.generateToken(user);

        return {
            user: user.toJSON(),
            token
        };
    }

    static async login(phoneNumber, password) {
        // Find user
        const user = await User.findByPhone(phoneNumber);
        if (!user) {
            throw new AppError('Invalid phone number or password', 401);
        }

        // Check password
        const isPasswordValid = await user.comparePassword(password);
        if (!isPasswordValid) {
            throw new AppError('Invalid phone number or password', 401);
        }

        // Generate token
        const token = this.generateToken(user);

        return {
            user: user.toJSON(),
            token
        };
    }

    static async getProfile(userId) {
        const user = await User.findById(userId);
        if (!user) {
            throw new AppError('Invalid Id', 401);
        }
    
        const userInterests = await User.getUserInterests(userId); 
        const getCategories = await settingsService.getQuestionCategory();
        const totalEarned = await userRewardService.getUserTotalReward(userId);
    
        if (userInterests && userInterests.categories) {
           
            // Parse the categories from string to array
            const userCategoryIds = userInterests.categories;
    
            // Filter matching categories
            const matchedCategories = getCategories.filter(category => 
                userCategoryIds.includes(category.id.toString())
            );
    
            // Attach to user object
            user.categories = matchedCategories;
        }

        if (totalEarned) {
            // Attach to user object
            user.totalEarned = totalEarned.total_reward ?? 0;
        }
    
        return user.toJSON();
    }
    

    
    
    static async updateProfile(userId, userData) {
        const user = await User.findById(userId);
        if (!user) {
            throw new AppError('Invalid Id', 401);
        }
       
        await User.updateProfile(userId,userData);
        const userUpdated = await User.findById(userId);
        const userInterests = await User.getUserInterests(userId); 
        const getCategories = await settingsService.getQuestionCategory();
    
        if (userInterests && userInterests.categories) {
            // Parse the categories from string to array
            const userCategoryIds = userInterests.categories;
    
            // Filter matching categories
            const matchedCategories = getCategories.filter(category => 
                userCategoryIds.includes(category.id.toString())
            );
    
            // Attach to user object
            userUpdated.categories = matchedCategories;
        }
      
        return userUpdated;
    }; 

    static generateToken(user) {
        return jwt.sign(
            { 
                id: user.id,
                phoneNumber: user.phoneNumber
            },
            process.env.JWT_SECRET,
            { expiresIn: '24h' }
        );
    }
}

module.exports = AuthService; 