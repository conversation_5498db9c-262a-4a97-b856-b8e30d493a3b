const { pool } = require('../config/config');

/**
 * RecentActivity Model
 * Represents the tbl_recent_activity table with structure:
 * - id (bigint, auto-increment, primary key)
 * - userId (varchar)
 * - activity (varchar)
 * - createdAt (datetime)
 * - updateAt (datetime)
 */
class RecentActivity {
    constructor(activity) {
        this.id = activity.id;
        this.userId = activity.userId;
        this.activity = activity.activity;
        this.langId = activity.langId;
        this.createdAt = activity.createdAt;
        this.updateAt = activity.updateAt;
    }

    /**
     * Create a new activity record
     * @param {Object} activityData - The activity data to insert
     * @returns {Promise} - Promise with the result of the insert operation
     */
    static async create(activityData) {
        try{
         const query = 'INSERT INTO tbl_recent_activity (userId, activity, createdAt, updateAt) VALUES (?, ?, NOW(), NOW())';
         await pool.query(query,[activityData.userId, activityData.activity]);
        }catch(err){
         console.log(err);
        }
       
    }

    /**
     * Get activities by user ID (limited to top 5 most recent)
     * @param {string} userId - The user ID to filter by
     * @returns {Promise} - Promise with the top 5 most recent activities for the user
     */
    static async getByUserId(userId) {
       try {
        const query = 'SELECT * FROM tbl_recent_activity WHERE userId = ? ORDER BY createdAt DESC LIMIT 5';
       const [result] = await pool.query(query,[userId]);
       return result;
       } catch (error) {
        console.log(error);
       }
    }

    
}

module.exports = RecentActivity;