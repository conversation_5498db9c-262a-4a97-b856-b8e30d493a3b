const { pool } = require('../config/config');

class QuestionCategoryTranslation {
    constructor(translation) {
        this.id = translation.id;
        this.langId = translation.langId;
        this.name = translation.name;
        this.category_icon_id = translation.category_icon_id;
        this.createdAt = translation.createdAt;
        this.updatedAt = translation.updatedAt;
    }

    static async findAll() {
        const [rows] = await pool.query('SELECT * FROM tbl_questions_categories_translation ORDER BY createdAt DESC');
        return rows.map(row => new QuestionCategoryTranslation(row));
    }

    static async findById(id) {
        const [rows] = await pool.query('SELECT * FROM tbl_questions_categories_translation WHERE id = ?', [id]);
        return rows[0] ? new QuestionCategoryTranslation(rows[0]) : null;
    }

    static async findByLanguage(langId) {
        const [rows] = await pool.query('SELECT * FROM tbl_questions_categories_translation WHERE langId = ? ORDER BY createdAt DESC', [langId]);
        return rows.map(row => new QuestionCategoryTranslation(row));
    }

    static async findByCategoryIconId(categoryIconId) {
        const [rows] = await pool.query('SELECT * FROM tbl_questions_categories_translation WHERE category_icon_id = ?', [categoryIconId]);
        return rows.map(row => new QuestionCategoryTranslation(row));
    }

    static async findByCategoryIconAndLanguage(categoryIconId, langId) {
        const [rows] = await pool.query('SELECT * FROM tbl_questions_categories_translation WHERE category_icon_id = ? AND langId = ?', [categoryIconId, langId]);
        return rows[0] ? new QuestionCategoryTranslation(rows[0]) : null;
    }

    static async create(translationData) {
        const [result] = await pool.query(
            'INSERT INTO tbl_questions_categories_translation (langId, name, category_icon_id, createdAt, updatedAt) VALUES (?, ?, ?, NOW(), NOW())',
            [translationData.langId, translationData.name, translationData.category_icon_id]
        );
        return this.findById(result.insertId);
    }

    static async update(id, translationData) {
        await pool.query(
            'UPDATE tbl_questions_categories_translation SET langId = ?, name = ?, category_icon_id = ?, updatedAt = NOW() WHERE id = ?',
            [translationData.langId, translationData.name, translationData.category_icon_id, id]
        );
        return this.findById(id);
    }

    static async upsert(categoryIconId, langId, translationData) {
        const existing = await this.findByCategoryIconAndLanguage(categoryIconId, langId);
        if (existing) {
            return await this.update(existing.id, { ...translationData, category_icon_id: categoryIconId, langId });
        } else {
            return await this.create({ ...translationData, category_icon_id: categoryIconId, langId });
        }
    }

    static async delete(id) {
        const [result] = await pool.query('DELETE FROM tbl_questions_categories_translation WHERE id = ?', [id]);
        return result.affectedRows > 0;
    }

    static async deleteByCategoryIconId(categoryIconId) {
        const [result] = await pool.query('DELETE FROM tbl_questions_categories_translation WHERE category_icon_id = ?', [categoryIconId]);
        return result.affectedRows;
    }

    static async getCategoriesWithTranslations(langId = null) {
        let query = `
            SELECT 
                qc.*,
                qct.name as translated_name,
                qct.langId,
                ci.icon
            FROM tbl_questions_categories qc
            LEFT JOIN tbl_questions_categories_translation qct ON qc.id = qct.category_icon_id
            LEFT JOIN tbl_categories_icons ci ON qc.id = ci.id
        `;
        
        let params = [];
        if (langId) {
            query += ' WHERE qct.langId = ? OR qct.langId IS NULL';
            params.push(langId);
        }
        
        query += ' ORDER BY qc.id ASC';

        const [rows] = await pool.query(query, params);
        return rows;
    }
}

module.exports = QuestionCategoryTranslation;
