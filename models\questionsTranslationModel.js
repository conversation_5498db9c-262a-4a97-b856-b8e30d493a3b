const { pool } = require('../config/config');

class QuestionTranslation {
    constructor(translation) {
        this.id = translation.id;
        this.question = translation.question;
        this.questionId = translation.questionId;
        this.langId = translation.langId;
        this.createdAt = translation.createdAt;
        this.updatedAt = translation.updatedAt;
    }

    static async findAll() {
        const [rows] = await pool.query('SELECT * FROM tbl_questions_translation ORDER BY createdAt DESC');
        return rows.map(row => new QuestionTranslation(row));
    }

    static async findById(id) {
        const [rows] = await pool.query('SELECT * FROM tbl_questions_translation WHERE id = ?', [id]);
        return rows[0] ? new QuestionTranslation(rows[0]) : null;
    }

    static async findByQuestionId(questionId) {
        const [rows] = await pool.query('SELECT * FROM tbl_questions_translation WHERE questionId = ?', [questionId]);
        return rows.map(row => new QuestionTranslation(row));
    }

    static async findByQuestionAndLanguage(questionId, langId) {
        const [rows] = await pool.query('SELECT * FROM tbl_questions_translation WHERE questionId = ? AND langId = ?', [questionId, langId]);
        return rows[0] ? new QuestionTranslation(rows[0]) : null;
    }

    static async findByLanguage(langId) {
        const [rows] = await pool.query('SELECT * FROM tbl_questions_translation WHERE langId = ? ORDER BY createdAt DESC', [langId]);
        return rows.map(row => new QuestionTranslation(row));
    }

    static async create(translationData) {
        const [result] = await pool.query(
            'INSERT INTO tbl_questions_translation (question, questionId, langId, createdAt, updatedAt) VALUES (?, ?, ?, NOW(), NOW())',
            [translationData.question, translationData.questionId, translationData.langId]
        );
        return this.findById(result.insertId);
    }

    static async update(id, translationData) {
        await pool.query(
            'UPDATE tbl_questions_translation SET question = ?, questionId = ?, langId = ?, updatedAt = NOW() WHERE id = ?',
            [translationData.question, translationData.questionId, translationData.langId, id]
        );
        return this.findById(id);
    }

    static async upsert(questionId, langId, translationData) {
        const existing = await this.findByQuestionAndLanguage(questionId, langId);
        if (existing) {
            return await this.update(existing.id, { ...translationData, questionId, langId });
        } else {
            return await this.create({ ...translationData, questionId, langId });
        }
    }

    static async delete(id) {
        const [result] = await pool.query('DELETE FROM tbl_questions_translation WHERE id = ?', [id]);
        return result.affectedRows > 0;
    }

    static async deleteByQuestionId(questionId) {
        const [result] = await pool.query('DELETE FROM tbl_questions_translation WHERE questionId = ?', [questionId]);
        return result.affectedRows;
    }

    static async getQuestionsWithTranslations(langId = null, limit = null) {
        let query = `
            SELECT 
                q.*,
                qt.question as translated_question,
                qt.langId
            FROM tbl_questions q
            LEFT JOIN tbl_questions_translation qt ON q.id = qt.questionId
        `;
        
        let params = [];
        if (langId) {
            query += ' WHERE qt.langId = ? OR qt.langId IS NULL';
            params.push(langId);
        }
        
        query += ' ORDER BY q.id DESC';
        
        if (limit) {
            query += ' LIMIT ?';
            params.push(limit);
        }

        const [rows] = await pool.query(query, params);
        return rows;
    }
}

module.exports = QuestionTranslation;
