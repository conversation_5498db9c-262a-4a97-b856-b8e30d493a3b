const request = require('supertest');
const express = require('express');
const { pool } = require('../config/config');
const routes = require('../routes/routes');

const app = express();
app.use(express.json());
app.use('/', routes);

describe('POST /auth/change-password', () => {
    let token;
    let oldPassword = 'password123';
    let newPassword = 'newpassword456';
    let phoneNumber = '1234567890';
    let server;

    beforeAll(async () => {
        server = app.listen(3002);
        // Clean up the user if it exists
        await pool.query('DELETE FROM tbl_users WHERE phoneNumber = ?', [phoneNumber]);

        // Register a new user
        await request(app)
            .post('/auth/register')
            .send({
                fullName: 'Test User',
                phoneNumber: phoneNumber,
                password: oldPassword,
                isProfileImageAvailable: '0',
                avatar: 'avatar.png',
                categories: JSON.stringify([1, 2, 3])
            });

        // Login to get a token
        const res = await request(app)
            .post('/auth/login')
            .send({
                phoneNumber: phoneNumber,
                password: oldPassword
            });
        token = res.body.data.token;
    });

    afterAll(async () => {
        // Clean up the user
        await pool.query('DELETE FROM tbl_users WHERE phoneNumber = ?', [phoneNumber]);
        await pool.end();
        server.close();
    });

    it('should change the password successfully', async () => {
        const res = await request(app)
            .post('/auth/change-password')
            .set('Authorization', `Bearer ${token}`)
            .send({
                oldPassword: oldPassword,
                newPassword: newPassword
            });

        expect(res.statusCode).toEqual(200);
        expect(res.body.message).toBe('Password changed successfully');
    });

    it('should fail to login with the old password', async () => {
        const res = await request(app)
            .post('/auth/login')
            .send({
                phoneNumber: phoneNumber,
                password: oldPassword
            });

        expect(res.statusCode).toEqual(401);
        expect(res.body.message).toBe('Invalid credentials');
    });

    it('should login successfully with the new password', async () => {
        const res = await request(app)
            .post('/auth/login')
            .send({
                phoneNumber: phoneNumber,
                password: newPassword
            });

        expect(res.statusCode).toEqual(200);
        expect(res.body.message).toBe('Login successful');
    });
});
