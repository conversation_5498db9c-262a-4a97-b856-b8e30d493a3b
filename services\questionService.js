const Question = require('../models/questionModel');
const ChallengeUserMapping = require('../models/challengeUserMappingModel');
const { AppError } = require('../middleware/errorMiddleware');
const { pool } = require('../config/config');
const AskedQuestion = require('../models/askedQuestionModel');
const Settings = require('../models/settingsModel');

class QuestionService {
    static async getQuestionsByLevel(userId, challengeId) {
        // Get user's current level and progress
        const [currentProgress] = await pool.query(
            'SELECT * FROM tbl_challenge_user_mapping WHERE userId = ? AND challenge_id = ? AND status IN (1, 2)',
            [userId, challengeId]
        );


        if (!currentProgress[0]) {
            throw new AppError('Challenge not started. Please start the challenge first.', 400);
        }

        // Check if user has any lives left
        if (currentProgress[0].life <= 0) {
            throw new AppError('No lives remaining. Please restart the challenge.', 400);
        }

        const userLevel = currentProgress[0].level;

        // Determine question strength based on level
        let strength;
        if (userLevel <= 2) {
            strength = 'low';
        } else if (userLevel <= 4) {
            strength = 'medium';
        } else {
            strength = 'high';
        }

        const questions = await Question.findQuestionsByLevelAndStrength(userId, userLevel, strength);

        // Handle case when no questions found
        if (questions.length === 0) {
            return {
                questions: [],
                summary: {
                    total: 0,
                    byType: {}
                },
                progress: {
                    level: userLevel,
                    score: currentProgress[0].score,
                    status: currentProgress[0].status,
                    life: currentProgress[0].life
                },
                message: `No new questions available for level ${userLevel} with ${strength} strength. Try again later.`
            };
        }

        // Group questions by type for better organization
        return {
            questions,
            summary: {
                total: questions.length,
                byType: questions.reduce((acc, q) => {
                    acc[q.type] = (acc[q.type] || 0) + 1;
                    return acc;
                }, {})
            },
            progress: {
                level: userLevel,
                score: currentProgress[0].score,
                status: currentProgress[0].status,
                life: currentProgress[0].life
            }
        };
    }

    static async getQuestionsByType(type) {
        const validTypes = ['text', 'image', 'video', 'audio'];
        if (!validTypes.includes(type)) {
            throw new AppError('Invalid question type', 400);
        }

        const questions = await Question.findQuestionsByType(type);

        if (questions.length === 0) {
            return {
                questions: [],
                message: `No questions found of type ${type}`
            };
        }

        return questions;
    }

    static async submitAnswer(userId, challengeId, questionId, answerId) {
        // Verify the challenge is started and not completed
        const [currentProgress] = await pool.query(
            'SELECT * FROM tbl_challenge_user_mapping WHERE userId = ? AND challenge_id = ? AND status IN (1, 2)',
            [userId, challengeId]
        );

        // console.log('currentProgress',currentProgress);

        if (!currentProgress[0]) {
            throw new AppError('Challenge not started. Please start the challenge first.', 400);
        }

        // Check if user has any lives left
        if (currentProgress[0].life <= 0) {
            throw new AppError('No lives remaining. Please restart the challenge.', 400);
        }
       await AskedQuestion.markQuestionAsAsked(userId, questionId);
        // Verify the question exists
        const question = await Question.getQuestionById(questionId);

        if (!question) {
            throw new AppError('Question not found', 404);
        }

        // Find the correct answer
        const correctAnswer = question.choices.find(choice => choice.isAnswer === 1);
        if (!correctAnswer) {
            throw new AppError('Question has no correct answer defined', 500);
        }

        // Check if the answer is correct
        const isCorrect = correctAnswer.id === answerId;

        let scoreIncrease = 0;
        let updatedProgress;
        let finalUserLevel = 1; // Will store the final user level
        let initialUserLevel = 0; // Will store the initial user level

        if (isCorrect) {
            // Calculate score based on question strength and correctness
            switch (question.strength) {
                case 'low':
                    scoreIncrease = 10;
                    break;
                case 'medium':
                    scoreIncrease = 20;
                    break;
                case 'high':
                    scoreIncrease = 30;
                    break;
                default:
                    scoreIncrease = 10;
            }

            // // Bonus points for media type questions
            // if (['image', 'video', 'audio'].includes(question.type)) {
            //     scoreIncrease += 5; // Additional points for media questions
            // }



            // Update progress with new score
            updatedProgress = await ChallengeUserMapping.updateProgress(
                userId,
                challengeId,
                scoreIncrease
            );

            // Get current level before updating
            initialUserLevel = updatedProgress.level;

            // Only allow incrementing level by 1 at a time
            // This ensures users don't skip levels even if their score jumps significantly
            finalUserLevel = initialUserLevel + 1;

            // Get level settings to check if user qualifies for next level
            const settings = await Settings.getLevelSettings();

            // Sort settings by level to ensure we process them in order
            const sortedSettings = settings.sort((a, b) => a.level - b.level);

            // Find the next level setting
            const nextLevelSetting = sortedSettings.find(setting => setting.level === finalUserLevel);

            // Check if user has enough score to level up
            const shouldLevelUp = nextLevelSetting && updatedProgress.score >= Number(nextLevelSetting.start);

            // Only level up if user has enough score for the next level
            if (shouldLevelUp) {
                // console.log(`User qualifies for level up from ${initialUserLevel} to ${finalUserLevel}`);

                if (nextLevelSetting.bonus) {
                    // Add level bonus to score
                    const levelBonus = Number(nextLevelSetting.bonus);
                    // console.log(`User leveled up to ${finalUserLevel}. Adding bonus: ${levelBonus}`);

                    // Update score with bonus
                    updatedProgress = await ChallengeUserMapping.updateProgress(
                        userId,
                        challengeId,
                        levelBonus
                    );

                    // Add bonus to scoreIncrease for the response
                    scoreIncrease += levelBonus;
                }

                // Update the user's level in the database
                await Question.updateLevel(userId, challengeId, finalUserLevel);
            } else {
                // User doesn't qualify for level up yet, keep the current level
                finalUserLevel = initialUserLevel;
            }

        } else {
            // Decrease life for wrong answer
             updatedProgress = await ChallengeUserMapping.updateLife(userId, challengeId, questionId, true);
        }

        // Prepare response
        const response = {
            isCorrect,
            scoreIncrease,
            questionType: question.type,
            progress: {
                ...updatedProgress,
                message: !isCorrect && updatedProgress.life <= 0 ?
                    'Game Over! No lives remaining.' :
                    undefined
            }
        };

        // Add levelUp information if user leveled up
        if (isCorrect && finalUserLevel > initialUserLevel) {
            // Get level settings again if needed
            const levelSettings = await Settings.getLevelSettings();
            const newLevelSetting = levelSettings.find(setting => setting.level === finalUserLevel);
            if (newLevelSetting && newLevelSetting.bonus) {
                response.levelUp = {
                    oldLevel: initialUserLevel,
                    newLevel: finalUserLevel,
                    bonus: Number(newLevelSetting.bonus)
                };
            }
        }

        return response;
    }
}

module.exports = QuestionService;