const ChallengeUserMappingModel = require('../models/challengeUserMappingModel');
const ChallengeModel = require('../models/challengeModel');

const ChallengeLifeService = {
  checkAndUpdateLifeByChallengeId: async (challengeId) => {
    const mapping = await ChallengeUserMappingModel.getMappingsByChallengeId(challengeId); // this should return a single row

    if (!mapping) {
      return { message: 'No mapping found for this challenge.' };
    }

    const challenge = await ChallengeModel.getChallengeById(challengeId);
    if (!challenge) {
      return { message: 'Challenge not found.' };
    }

    const lastUpdated = new Date(mapping.updatedAt);
    const now = new Date();
    const diffInHours = Math.abs(now - lastUpdated) / 36e5;

    // For testing: 0.01 hours ~ 36 seconds
    if (diffInHours >= 24) {
      await ChallengeUserMappingModel.refillLife(mapping.id, challenge.user_life);
      return { message: `Life updated for mapping ID ${mapping.id}` };
    }

    return { message: `Less than 36 seconds passed. No update made.` };
  }
};

module.exports = ChallengeLifeService;
