{"name": "<PERSON><PERSON><PERSON><PERSON>", "version": "1.0.0", "description": "", "main": "server.js", "start": "node server.js", "scripts": {"test": "jest", "clear": "npm cache clean --force", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "author": "<PERSON><PERSON><PERSON> wube", "license": "ISC", "dependencies": {"bcryptjs": "^3.0.2", "compression": "^1.8.0", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.21.2", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^1.4.5-lts.2", "mysql2": "^3.13.0", "node-cron": "^3.0.3", "socket.io": "^4.8.1"}, "devDependencies": {"jest": "^29.7.0", "supertest": "^7.1.4"}}