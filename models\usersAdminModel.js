const { pool } = require('../config/config');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');

class UserAdmin {
    constructor(admin) {
        this.id = admin.id;
        this.fullName = admin.fullName;
        this.username = admin.username;
        this.password = admin.password;
        this.createdAt = admin.createdAt;
        this.updatedAt = admin.updatedAt;
    }

    static async findByUsername(username) {
        const [rows] = await pool.query('SELECT * FROM tbl_users_admin WHERE username = ?', [username]);
        return rows[0] ? new UserAdmin(rows[0]) : null;
    }

    static async findById(id) {
        const [rows] = await pool.query('SELECT * FROM tbl_users_admin WHERE id = ?', [id]);
        return rows[0] ? new UserAdmin(rows[0]) : null;
    }

    static async findAll() {
        const [rows] = await pool.query('SELECT id, fullName, username, createdAt, updatedAt FROM tbl_users_admin ORDER BY createdAt DESC');
        return rows.map(row => new UserAdmin(row));
    }

    static async create(adminData) {
        const hashedPassword = await bcrypt.hash(adminData.password, 12);
        const [result] = await pool.query(
            'INSERT INTO tbl_users_admin (fullName, username, password, createdAt, updatedAt) VALUES (?, ?, ?, NOW(), NOW())',
            [adminData.fullName, adminData.username, hashedPassword]
        );
        return this.findById(result.insertId);
    }

    static async update(id, adminData) {
        let query = 'UPDATE tbl_users_admin SET fullName = ?, username = ?, updatedAt = NOW()';
        let params = [adminData.fullName, adminData.username];
        
        if (adminData.password) {
            const hashedPassword = await bcrypt.hash(adminData.password, 12);
            query += ', password = ?';
            params.push(hashedPassword);
        }
        
        query += ' WHERE id = ?';
        params.push(id);
        
        await pool.query(query, params);
        return this.findById(id);
    }

    static async delete(id) {
        const [result] = await pool.query('DELETE FROM tbl_users_admin WHERE id = ?', [id]);
        return result.affectedRows > 0;
    }

    async verifyPassword(password) {
        return await bcrypt.compare(password, this.password);
    }

    generateToken() {
        return jwt.sign(
            { id: this.id, username: this.username, type: 'admin' },
            process.env.JWT_SECRET,
            { expiresIn: '24h' }
        );
    }
}

module.exports = UserAdmin;
